# 学习Demo
# 创建人：ZY
# 开发时间：2022/12/8 9:27
import os  # 导入os模块，用于文件操作


filename = 'student.txt'   # 定义文件名


def main():  # 主函数
    while True:
        menu()  # 显示菜单
        choice = int(input('请选择'))
        if choice in [0, 1, 2, 3, 4, 5, 6, 7]:

            if choice == 0:
                answer = input('您确定要退出系统吗？y/n')
                if answer == 'y' or answer == 'Y':
                    print('谢谢你的使用')
                    break
                else:
                    continue
            elif choice == 1:
                insert()         # 录入学生信息
            elif choice == 2:
                search()         # 查找学生信息 # 这里的search()函数没有定义，需要自己定义
            elif choice == 3:    # 这里的delete()函数没有定义，需要自己定义
                delete()         # 删除学生信息
            elif choice == 4:
                modify()         # 修改学生信息 # 这里的modify()函数没有定义，需要自己定义
            elif choice == 5:
                sort()           # 排序
            elif choice == 6:
                total()          # 统计学生总人数
            elif choice == 7:
                show()           # 显示所有学生总人数


def menu():
    print('====================================学生信息管理系统=======================================')
    print('-------------------------------------功能菜单---------------------------------------------')
    print('\t\t\t\t\t\t\t\t\t1.录入学生信息')  #
    print('\t\t\t\t\t\t\t\t\t2.查找学生信息')
    print('\t\t\t\t\t\t\t\t\t3.删除学生信息')
    print('\t\t\t\t\t\t\t\t\t4.修改学生信息')
    print('\t\t\t\t\t\t\t\t\t5.排序')
    print('\t\t\t\t\t\t\t\t\t6.统计学生总人数')
    print('\t\t\t\t\t\t\t\t\t7.显示所有学生总人数')
    print('\t\t\t\t\t\t\t\t\t0.退出')
    print('========================================================================================')


def insert():
    student_list = []  # 保存学生信息的空列表
    while True:
        id = input('请输入你的id，如：1001')
        if not id:                          # 判断为真，空列表的BOOL值为False,取反则为TRUE # 判断输入是否为空
            break
        name = input('请输入你的姓名：')
        if not name:
            break
        try:                                 # 输入的结果正确的情况下
            english = int(input('请输入你的english成绩'))
            python = int(input('请输入你的python成绩'))
            java = int(input('请输入你的java成绩'))
        except:                               # 异常的情况
            print('输入无效，不是整数类型，请重新输入')
            continue
        # 将录入的学生信息保存到字典
        student = {'id': id, 'name': name, 'english': english, 'python': python, 'java': java}  # 这里的student是一个字典
        # 将学生信息添加到列表
        student_list.append(student)
        answer = input('是否继续添加？y/n')
        if answer == 'y' or answer == 'Y':
            continue
        else:
            break
# 调用save()函数，进行保存
    save(student_list)  # 调用save()函数，进行保存
    print('学生信息录入完毕')


def save(lst):
    try:
        stu_txt = open(filename, 'a', encoding='utf-8')    # a已添加方式打开 encoding 防止中文乱码
    except:
        stu_txt = open(filename, 'w', encoding='utf-8')    # w 以写的方式打来，覆盖原来的内容（如果有）
    for item in lst:                                       # 遍历列表
        stu_txt.write((str(item))+'\n')                    # \n 换行输出 #   这里的str(item)是将字典转成字符串
    stu_txt.close()                                        # 关闭文件,这里注意和 for 对齐


def search():
    student_query = []   # 定义一个空列表用于存储数据
    while True:
        id = ''      # 声明一个空id
        name = ''
        if os.path.exists(filename):   # 判断文件是否存在
            mode = input('请输入您的查询模式：1.按id查询 2.按姓名查询')
            if mode == "1":        # 这里查询的数是字符串
                id = input('请输入你要查询的id：')
            elif mode == "2":
                name = input('请输入你要查询的姓名：')
            else:
                print('您输入的模式不正确，请重新输入')
                search()   # 调用自身继续查询
            with open(filename, 'r', encoding='utf-8')as rfile:
                student = rfile.readlines()   # 读取文件内容，并按行分割成列表
                for item in student:
                    d = dict(eval(item))    # 将字符串转成字典
                    if id != '':
                        if d['id'] == id:
                            student_query.append(d)
                    elif name != '':           # 这里缺少判断，这部分没写 而且缩进也有错误
                        if d['name'] == name:   # 这里的name是字符串，需要用==判断
                            student_query.append(d)
            # 显示查询结果
            show_student(student_query)
            # 清空列表
            student_query.clear()
            answer = input('是否要继续查询？y/n\n')
            if answer == 'Y' or answer == 'y':
                continue
            else:
                break
        else:
            print('暂未保存学生信息')
            return


def show_student(lst: object) -> object:      # lst 定义函数时的形参
    if len(lst) == 0:   # 如果列表的长度=0
        print('没有查询到学生信息，无数据显示')
        return
    # 定义标题显示格式   格式化标题    对应id 姓名 英语格式 java成绩 Python成绩
    format_title = '{:^6}\t{:^12}\t{:^8}\t{:^10}\t{:^10}\t{:^8}'       # {0:^30}中的0是一个序号，表示格式化输出的第0个字符，依次累加
    print(format_title.format('ID', '姓名', '英语成绩', 'python成绩', 'Java成绩', '总成绩'))       # 填充标题
# {0:^30}中的30表示输出宽度约束为30个字符  {0:^30}中的^表示输出时右对齐，若宽度小于字符串的实际宽度，以实际宽度输出
# 定义内容的显示格式
    format_data = '{:^6}\t{:^12}\t{:^8}\t{:^8}\t{:^8}\t{:^8}'    # 这里的花括号我多写了一个
    for item in lst:
        print(format_data.format(item.get('id'),
                                 item.get('name'),
                                 item.get('english'),
                                 item.get('python'),
                                 item.get('java'),
                                 int(item.get('english'))+int(item.get('python'))+int(item.get('java'))))


# """if student_id != ''，说明有内容，相当于： if not id"""


def delete():
    while True:
        student_id = input('请输入你要删除的学生ID：')
        if student_id != '':
            if os.path.exists(filename):                   # exists判断文件目录是否存在
                with open(filename, 'r', encoding='utf-8')as file:  # r 以只读模式打开文件
                    student_old = file.readlines()          # readlines(）把文件中每一行都作为独立的字符创对象，并将这些对象放入列表返回
            else:
                student_old = []                           # 文件不存在，定义文件为空,注意我这里的readlines漏写了个s 找了很久问题
            flag = False         # 标记是否删除
            if student_old:  # 磁盘上有学生信息
                with open(filename, 'w', encoding='utf-8')as wfile:
                    d = {}
                    for item in student_old:
                        d = dict(eval(item))                # 将字符串转成字典
                        if d['id'] != student_id:           # [] 获取的是字典的值 d['id'] 用于新增元素或者比较值
                            wfile.write(str(d) + '\n')
                        else:
                            flag = True                     # 表示已经删除
                    if flag:
                        print(f'id为{student_id}的学生信息已经被删除')         # f为格式化字符串format.的简写
                    else:
                        print(f'没有找到id为{student_id}的学生信息')
            else:                                                          # 磁盘上没有学生信息
                print('无学生信息')
                break
            show()                                                          # 删除之后要重新显示学生信息
            answer = input('是否要继续删除学生信息y/n\n')
            if answer == 'y' or answer == 'Y':
                continue
            else:
                break


def modify():
    show()
    if os.path.exists(filename):
        with open(filename, 'r', encoding='utf-8')as rfile:
            student_old = rfile.readlines()
    else:
        return          # 这里先判断有没有filename这个文件，如果有将内容读取到student_old里面；没有就返回
    student_id = input('请输入您要修改的学生id：')
    with open(filename, 'w', encoding='utf-8') as wfile:
        for item in student_old:
            d = dict(eval(item))
            if d['id'] == student_id:
                print('找到学生信息，可以修改他的相关信息')
                while True:
                    try:
                        d['id'] = input('要修改的学生id为：')
                        d['name'] = input('要修改的学生名字为：')
                        d['english'] = input('要修改的英语成绩为：')
                        d['python'] = input('要修改的python成绩为：')
                        d['java'] = input('要修改的Java成绩为：')
                    except:
                        print('对不起，您输入的有误')
                    else:
                        break
                wfile.write(str(d)+'\n')              # 在filename里面写入字符串 str是d转换为字符串；
                print('修改成功！！！')
            else:
                wfile.write(str(d) + '\n')              # id不相等，将原来的信息写入
        answer = input('是否要继续修改其它学生信息？y/n')
        if answer == 'y' or answer == 'Y':
            modify()         # 调用自身继续修改


def sort():
    show()
    if os.path.exists(filename):                        # 判断文件是否存在
        with open(filename, 'r', encoding='utf-8')as rfile:
            student_list = rfile.readlines()
        student_new = []     # 用来存储字符串转换为字典的数据
        for item in student_list:
            d = dict(eval(item))
            student_new.append(d)
    else:
        return
    asc_or_desc = input('请选者（0表示升序，1表示降序）：')
    if asc_or_desc == '0':
        asc_or_desc_bool = False   # 这里的asc_or_desc_bool是布尔类型
    elif asc_or_desc == '1':
        asc_or_desc_bool = True
    else:
        print('你的输入有误，请重新输入')
        sort()
    mode = input('请选择你的排序方式（1.按英语成绩排序 2.按python成绩排序 3.按java成绩排序 0.按总成绩排序）：')
    if mode == '1':
        student_new.sort(key=lambda x: int(x['english']), reverse=asc_or_desc_bool)           # 这里的key=lambda x: int(x['english'])是字典的键值对，reverse=asc_or_desc_bool是排序方式
    elif mode == '2':
        student_new.sort(key=lambda x: int(x['python']), reverse=asc_or_desc_bool)
    elif mode == '3':
        student_new.sort(key=lambda x: int(x['python']), reverse=asc_or_desc_bool)
    elif mode == '0':
        student_new.sort(key=lambda x: int(x['english'])+int(x['python'])+int(x['python']), reverse=asc_or_desc_bool)
    else:
        print('你的输入有误，请重新输入！！！')
        sort()   # 调用自身继续排序
    show_student(student_new)  # 调用之前定义的show_student函数 # 这里的student_new是排序后的列表



def total():                  # 统计学生的人数实际上就是统计列表的长度
    if os.path.exists(filename):
        with open(filename, 'r', encoding='utf-8')as rfile:
            students = rfile.readlines()
            if students:
                print(f'一共有{len(students)}名学生')
            else:
                print('还没有录入学生信息')
    else:
        print('暂未保存学生信息')


def show():    # 查询显示所有学生信息
    student_lst = []
    if os.path.exists(filename):
        with open(filename, 'r', encoding='utf-8')as rfile:         # as 为别称
            students = rfile.readlines()
            for item in students:
                student_lst.append(eval(item))
                if student_lst:
                    show_student(student_lst)   # 调用之前定义的show_student
                else:
                    print('请录入你的学生信息')
    print('暂未保存数据信息')


if __name__ == '__main__':
    main()
