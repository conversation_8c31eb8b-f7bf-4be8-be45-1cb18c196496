=====================================================
TIA Portal V16 变量表手动输入模板
电机顺序控制系统
=====================================================

在TIA Portal中手动创建变量表的步骤：

1. 打开TIA Portal V16
2. 项目树 → PLC_1 → PLC变量表 → 默认变量表
3. 双击打开变量表
4. 按照下表逐行输入变量

=====================================================
输入变量 (Input Variables)
=====================================================

行1:
名称: I_Start_Button
数据类型: Bool
地址: %I0.0
注释: 启动按钮

行2:
名称: I_Stop_Button
数据类型: Bool
地址: %I0.1
注释: 正常停止按钮

行3:
名称: I_Emergency_Stop
数据类型: Bool
地址: %I0.2
注释: 紧急停止按钮(常闭)

行4:
名称: I_Motor1_Thermal
数据类型: Bool
地址: %I0.3
注释: 电机1热保护(常闭)

行5:
名称: I_Motor2_Thermal
数据类型: Bool
地址: %I0.4
注释: 电机2热保护(常闭)

行6:
名称: I_Motor3_Thermal
数据类型: Bool
地址: %I0.5
注释: 电机3热保护(常闭)

行7:
名称: I_Motor4_Thermal
数据类型: Bool
地址: %I0.6
注释: 电机4热保护(常闭)

行8:
名称: I_Motor5_Thermal
数据类型: Bool
地址: %I0.7
注释: 电机5热保护(常闭)

行9:
名称: I_Reset_Button
数据类型: Bool
地址: %I1.0
注释: 复位按钮

=====================================================
输出变量 (Output Variables)
=====================================================

行10:
名称: Q_Motor1
数据类型: Bool
地址: %Q0.0
注释: 电机1输出

行11:
名称: Q_Motor2
数据类型: Bool
地址: %Q0.1
注释: 电机2输出

行12:
名称: Q_Motor3
数据类型: Bool
地址: %Q0.2
注释: 电机3输出

行13:
名称: Q_Motor4
数据类型: Bool
地址: %Q0.3
注释: 电机4输出

行14:
名称: Q_Motor5
数据类型: Bool
地址: %Q0.4
注释: 电机5输出

行15:
名称: Q_System_Ready
数据类型: Bool
地址: %Q0.5
注释: 系统就绪指示灯

行16:
名称: Q_System_Running
数据类型: Bool
地址: %Q0.6
注释: 系统运行指示灯

行17:
名称: Q_Alarm
数据类型: Bool
地址: %Q0.7
注释: 报警指示灯

=====================================================
快速复制粘贴版本 (用于Excel或记事本)
=====================================================

I_Start_Button	Bool	%I0.0	启动按钮
I_Stop_Button	Bool	%I0.1	正常停止按钮
I_Emergency_Stop	Bool	%I0.2	紧急停止按钮(常闭)
I_Motor1_Thermal	Bool	%I0.3	电机1热保护(常闭)
I_Motor2_Thermal	Bool	%I0.4	电机2热保护(常闭)
I_Motor3_Thermal	Bool	%I0.5	电机3热保护(常闭)
I_Motor4_Thermal	Bool	%I0.6	电机4热保护(常闭)
I_Motor5_Thermal	Bool	%I0.7	电机5热保护(常闭)
I_Reset_Button	Bool	%I1.0	复位按钮
Q_Motor1	Bool	%Q0.0	电机1输出
Q_Motor2	Bool	%Q0.1	电机2输出
Q_Motor3	Bool	%Q0.2	电机3输出
Q_Motor4	Bool	%Q0.3	电机4输出
Q_Motor5	Bool	%Q0.4	电机5输出
Q_System_Ready	Bool	%Q0.5	系统就绪指示灯
Q_System_Running	Bool	%Q0.6	系统运行指示灯
Q_Alarm	Bool	%Q0.7	报警指示灯

=====================================================
注意事项:
1. 地址格式必须包含%符号
2. 数据类型首字母大写 (Bool, Int, Word等)
3. 变量名不能包含空格和特殊字符
4. 中文注释可能需要设置正确的字符编码
5. 保存后可以在程序中直接使用这些变量名
=====================================================
