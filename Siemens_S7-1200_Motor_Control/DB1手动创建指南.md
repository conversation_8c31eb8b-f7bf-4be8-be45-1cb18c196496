# DB1数据块手动创建指南

## 🎯 创建DB1数据块步骤

### 步骤1：创建数据块
1. 右键"程序块" → "添加新块"
2. 块类型：数据块 (DB)
3. 名称：`Motor_Control_DB`
4. 编号：1
5. 类型：全局数据块
6. 点击确定

### 步骤2：定义数据结构

在数据块编辑器中按以下顺序添加变量：

#### 主要数据项

| 名称 | 数据类型 | 初始值 | 注释 |
|------|----------|--------|------|
| Motor_Sequence_Control | FB1_Motor_Sequence_Control | - | 电机顺序控制功能块实例 |
| Start_Delay_Time | Time | T#3s | 启动延时时间(3秒) |
| Stop_Delay_Time | Time | T#5s | 停止延时时间(5秒) |
| System_Status | Word | 0 | 系统状态字 |
| Motor_Status | Word | 0 | 电机状态字 |
| Alarm_Status | Word | 0 | 报警状态字 |

#### 统计数据项

| 名称 | 数据类型 | 初始值 | 注释 |
|------|----------|--------|------|
| Start_Counter | DInt | 0 | 启动次数计数器 |
| Emergency_Stop_Counter | DInt | 0 | 紧急停止次数计数器 |
| Thermal_Trip_Counter | DInt | 0 | 热保护跳闸次数计数器 |

#### 运行时间统计

| 名称 | 数据类型 | 初始值 | 注释 |
|------|----------|--------|------|
| Motor1_Runtime | Time | T#0ms | 电机1运行时间 |
| Motor2_Runtime | Time | T#0ms | 电机2运行时间 |
| Motor3_Runtime | Time | T#0ms | 电机3运行时间 |
| Motor4_Runtime | Time | T#0ms | 电机4运行时间 |
| Motor5_Runtime | Time | T#0ms | 电机5运行时间 |

#### 诊断信息

| 名称 | 数据类型 | 初始值 | 注释 |
|------|----------|--------|------|
| Last_Start_Time | Date_And_Time | DT#1990-1-1-0:0:0.0 | 最后启动时间 |
| Last_Stop_Time | Date_And_Time | DT#1990-1-1-0:0:0.0 | 最后停止时间 |
| Last_Alarm_Time | Date_And_Time | DT#1990-1-1-0:0:0.0 | 最后报警时间 |

## 🔧 详细创建步骤

### 步骤1：添加FB实例
1. 在数据块编辑器第1行：
   - **名称**：`Motor_Sequence_Control`
   - **数据类型**：`FB1_Motor_Sequence_Control`
   - **注释**：`电机顺序控制功能块实例`

### 步骤2：添加系统参数
2. 第2行：
   - **名称**：`Start_Delay_Time`
   - **数据类型**：`Time`
   - **初始值**：`T#3s`
   - **注释**：`启动延时时间(3秒)`

3. 第3行：
   - **名称**：`Stop_Delay_Time`
   - **数据类型**：`Time`
   - **初始值**：`T#5s`
   - **注释**：`停止延时时间(5秒)`

### 步骤3：添加状态字
4. 第4行：
   - **名称**：`System_Status`
   - **数据类型**：`Word`
   - **初始值**：`0`
   - **注释**：`系统状态字`

5. 第5行：
   - **名称**：`Motor_Status`
   - **数据类型**：`Word`
   - **初始值**：`0`
   - **注释**：`电机状态字`

6. 第6行：
   - **名称**：`Alarm_Status`
   - **数据类型**：`Word`
   - **初始值**：`0`
   - **注释**：`报警状态字`

### 步骤4：添加计数器
7. 第7行：
   - **名称**：`Start_Counter`
   - **数据类型**：`DInt`
   - **初始值**：`0`
   - **注释**：`启动次数计数器`

8. 第8行：
   - **名称**：`Emergency_Stop_Counter`
   - **数据类型**：`DInt`
   - **初始值**：`0`
   - **注释**：`紧急停止次数计数器`

9. 第9行：
   - **名称**：`Thermal_Trip_Counter`
   - **数据类型**：`DInt`
   - **初始值**：`0`
   - **注释**：`热保护跳闸次数计数器`

### 步骤5：添加运行时间统计
10-14行：依次添加5个电机的运行时间
   - **数据类型**：`Time`
   - **初始值**：`T#0ms`

### 步骤6：添加诊断时间
15-17行：添加时间戳
   - **数据类型**：`Date_And_Time`
   - **初始值**：`DT#1990-1-1-0:0:0.0`

## 📋 简化版DB创建

如果觉得太复杂，可以创建简化版本，只包含必需项：

### 最小化DB结构
```
名称                        数据类型                    注释
Motor_Sequence_Control     FB1_Motor_Sequence_Control  功能块实例
System_Status              Word                        系统状态
Motor_Status               Word                        电机状态
Start_Counter              DInt                        启动计数
```

## 🔧 创建技巧

### 数据类型说明：
- **FB1_Motor_Sequence_Control**：功能块类型（必须先创建FB1）
- **Time**：时间类型，格式如 T#3s
- **Word**：16位字类型
- **DInt**：32位双整数
- **Date_And_Time**：日期时间类型

### 初始值格式：
- 时间：`T#3s`, `T#5s`, `T#0ms`
- 日期时间：`DT#1990-1-1-0:0:0.0`
- 数值：`0`

## ⚠️ 重要注意事项

1. **FB1必须先存在**：
   - 在创建DB1之前，必须先创建FB1功能块
   - 否则无法选择FB1_Motor_Sequence_Control数据类型

2. **数据类型匹配**：
   - 确保数据类型拼写正确
   - 注意大小写敏感

3. **保存和编译**：
   - 创建完成后保存 (Ctrl+S)
   - 编译检查无错误

## ✅ 完成验证

创建完成后应该看到：
- [ ] DB1数据块出现在程序块列表中
- [ ] 包含FB1实例和相关数据项
- [ ] 编译无错误
- [ ] 可以在OB1中调用

## 🚀 下一步

DB1创建完成后：
1. 创建OB1主程序
2. 在OB1中调用DB1.Motor_Sequence_Control
3. 连接输入输出变量
4. 测试程序功能

完成DB1后，我们就可以编写主程序OB1了！
