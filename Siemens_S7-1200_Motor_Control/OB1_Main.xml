<?xml version="1.0" encoding="UTF-8"?>
<Document>
  <Engineering version="V16" />
  <DocumentInfo>
    <Created>2025-01-29T00:00:00.0000000Z</Created>
    <ExportSetting>WithDefaults</ExportSetting>
  </DocumentInfo>
  <SW.Blocks.OB>
    <AttributeList>
      <Name>Main [OB1]</Name>
      <Number>1</Number>
      <ProgrammingLanguage>LAD</ProgrammingLanguage>
      <Version>0.1</Version>
      <Comment>
        <MultiLanguageText Lang="zh-CN">主程序循环</MultiLanguageText>
      </Comment>
    </AttributeList>
    <ObjectList>
      <MultilingualText>
        <ObjectList>
          <MultilingualTextItem>
            <AttributeList>
              <Culture>zh-CN</Culture>
              <Text>主程序 - 电机顺序控制系统
调用电机顺序控制功能块</Text>
            </AttributeList>
          </MultilingualTextItem>
        </ObjectList>
      </MultilingualText>
      
      <!-- 网络1: 调用电机顺序控制功能块 -->
      <SW.Blocks.CompileUnit>
        <AttributeList>
          <NetworkSource>
            <FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">
              <Parts>
                <!-- 功能块调用 -->
                <Access Scope="GlobalVariable" UId="21">
                  <Symbol>
                    <Component Name="I_Start_Button" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="22">
                  <Symbol>
                    <Component Name="I_Stop_Button" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="23">
                  <Symbol>
                    <Component Name="I_Emergency_Stop" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="24">
                  <Symbol>
                    <Component Name="I_Reset_Button" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="25">
                  <Symbol>
                    <Component Name="I_Motor1_Thermal" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="26">
                  <Symbol>
                    <Component Name="I_Motor2_Thermal" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="27">
                  <Symbol>
                    <Component Name="I_Motor3_Thermal" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="28">
                  <Symbol>
                    <Component Name="I_Motor4_Thermal" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="29">
                  <Symbol>
                    <Component Name="I_Motor5_Thermal" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="30">
                  <Symbol>
                    <Component Name="Motor_Control_DB" />
                  </Symbol>
                </Access>
                
                <!-- 热保护状态合并 -->
                <Part Name="And" UId="31">
                  <Inputs>
                    <Input UId="32" />
                    <Input UId="33" />
                    <Input UId="34" />
                    <Input UId="35" />
                    <Input UId="36" />
                  </Inputs>
                  <Output UId="37" />
                </Part>
                
                <!-- 功能块调用 -->
                <Part Name="Call" UId="38">
                  <Inputs>
                    <Input Name="Start_Cmd" UId="39" />
                    <Input Name="Stop_Cmd" UId="40" />
                    <Input Name="Emergency_Stop" UId="41" />
                    <Input Name="Reset_Cmd" UId="42" />
                    <Input Name="Thermal_Protection" UId="43" />
                  </Inputs>
                  <Outputs>
                    <Output Name="Motor1_Out" UId="44" />
                    <Output Name="Motor2_Out" UId="45" />
                    <Output Name="Motor3_Out" UId="46" />
                    <Output Name="Motor4_Out" UId="47" />
                    <Output Name="Motor5_Out" UId="48" />
                    <Output Name="System_Ready" UId="49" />
                    <Output Name="System_Running" UId="50" />
                    <Output Name="Alarm" UId="51" />
                  </Outputs>
                </Part>
                
                <!-- 输出连接 -->
                <Access Scope="GlobalVariable" UId="52">
                  <Symbol>
                    <Component Name="Q_Motor1" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="53">
                  <Symbol>
                    <Component Name="Q_Motor2" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="54">
                  <Symbol>
                    <Component Name="Q_Motor3" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="55">
                  <Symbol>
                    <Component Name="Q_Motor4" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="56">
                  <Symbol>
                    <Component Name="Q_Motor5" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="57">
                  <Symbol>
                    <Component Name="Q_System_Ready" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="58">
                  <Symbol>
                    <Component Name="Q_System_Running" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="59">
                  <Symbol>
                    <Component Name="Q_Alarm" />
                  </Symbol>
                </Access>
                
                <Part Name="Coil" UId="60">
                  <Input UId="61" />
                </Part>
                <Part Name="Coil" UId="62">
                  <Input UId="63" />
                </Part>
                <Part Name="Coil" UId="64">
                  <Input UId="65" />
                </Part>
                <Part Name="Coil" UId="66">
                  <Input UId="67" />
                </Part>
                <Part Name="Coil" UId="68">
                  <Input UId="69" />
                </Part>
                <Part Name="Coil" UId="70">
                  <Input UId="71" />
                </Part>
                <Part Name="Coil" UId="72">
                  <Input UId="73" />
                </Part>
                <Part Name="Coil" UId="74">
                  <Input UId="75" />
                </Part>
                
              </Parts>
              <Wires>
                <!-- 热保护状态合并 -->
                <Wire UId="76">
                  <Powerrail />
                  <NameCon UId="25" Name="in" />
                </Wire>
                <Wire UId="77">
                  <NameCon UId="25" Name="out" />
                  <NameCon UId="31" Name="in" UId="32" />
                </Wire>
                <Wire UId="78">
                  <NameCon UId="26" Name="out" />
                  <NameCon UId="31" Name="in" UId="33" />
                </Wire>
                <Wire UId="79">
                  <NameCon UId="27" Name="out" />
                  <NameCon UId="31" Name="in" UId="34" />
                </Wire>
                <Wire UId="80">
                  <NameCon UId="28" Name="out" />
                  <NameCon UId="31" Name="in" UId="35" />
                </Wire>
                <Wire UId="81">
                  <NameCon UId="29" Name="out" />
                  <NameCon UId="31" Name="in" UId="36" />
                </Wire>
                
                <!-- 功能块输入连接 -->
                <Wire UId="82">
                  <NameCon UId="21" Name="out" />
                  <NameCon UId="38" Name="in" UId="39" />
                </Wire>
                <Wire UId="83">
                  <NameCon UId="22" Name="out" />
                  <NameCon UId="38" Name="in" UId="40" />
                </Wire>
                <Wire UId="84">
                  <NameCon UId="23" Name="out" />
                  <NameCon UId="38" Name="in" UId="41" />
                </Wire>
                <Wire UId="85">
                  <NameCon UId="24" Name="out" />
                  <NameCon UId="38" Name="in" UId="42" />
                </Wire>
                <Wire UId="86">
                  <NameCon UId="31" Name="out" UId="37" />
                  <NameCon UId="38" Name="in" UId="43" />
                </Wire>
                
                <!-- 功能块输出连接 -->
                <Wire UId="87">
                  <NameCon UId="38" Name="out" UId="44" />
                  <NameCon UId="60" Name="in" UId="61" />
                </Wire>
                <Wire UId="88">
                  <NameCon UId="38" Name="out" UId="45" />
                  <NameCon UId="62" Name="in" UId="63" />
                </Wire>
                <Wire UId="89">
                  <NameCon UId="38" Name="out" UId="46" />
                  <NameCon UId="64" Name="in" UId="65" />
                </Wire>
                <Wire UId="90">
                  <NameCon UId="38" Name="out" UId="47" />
                  <NameCon UId="66" Name="in" UId="67" />
                </Wire>
                <Wire UId="91">
                  <NameCon UId="38" Name="out" UId="48" />
                  <NameCon UId="68" Name="in" UId="69" />
                </Wire>
                <Wire UId="92">
                  <NameCon UId="38" Name="out" UId="49" />
                  <NameCon UId="70" Name="in" UId="71" />
                </Wire>
                <Wire UId="93">
                  <NameCon UId="38" Name="out" UId="50" />
                  <NameCon UId="72" Name="in" UId="73" />
                </Wire>
                <Wire UId="94">
                  <NameCon UId="38" Name="out" UId="51" />
                  <NameCon UId="74" Name="in" UId="75" />
                </Wire>
              </Wires>
            </FlgNet>
          </NetworkSource>
        </AttributeList>
        <ObjectList>
          <MultilingualText>
            <ObjectList>
              <MultilingualTextItem>
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text>网络1: 调用电机顺序控制功能块
处理所有输入信号并输出到相应的输出点</Text>
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
        </ObjectList>
      </SW.Blocks.CompileUnit>
      
    </ObjectList>
    <SW.Blocks.BlockInterface>
      <AttributeList>
        <HeaderAuthor />
        <HeaderFamily />
        <HeaderName />
        <HeaderVersion>0.1</HeaderVersion>
      </AttributeList>
    </SW.Blocks.BlockInterface>
  </SW.Blocks.OB>
</Document>
