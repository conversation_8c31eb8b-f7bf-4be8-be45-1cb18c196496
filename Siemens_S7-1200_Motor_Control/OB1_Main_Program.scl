// =====================================================
// 主程序 OB1 - 电机顺序控制系统
// 编程语言：结构化控制语言 (SCL)
// 开发环境：TIA Portal V16
// =====================================================

ORGANIZATION_BLOCK "Main [OB1]"
{ S7_Optimized_Access := 'TRUE' }
VERSION : 0.1
   VAR_TEMP 
      Thermal_Status : Bool;   // 热保护综合状态
   END_VAR

BEGIN
// =====================================================
// 网络1: 热保护状态合并
// 所有电机热保护状态AND运算
// =====================================================
#Thermal_Status := "I_Motor1_Thermal" AND 
                   "I_Motor2_Thermal" AND 
                   "I_Motor3_Thermal" AND 
                   "I_Motor4_Thermal" AND 
                   "I_Motor5_Thermal";

// =====================================================
// 网络2: 调用电机顺序控制功能块
// =====================================================
"Motor_Control_DB".Motor_Sequence_Control(
    Start_Cmd := "I_Start_Button",
    Stop_Cmd := "I_Stop_Button", 
    Emergency_Stop := "I_Emergency_Stop",
    Reset_Cmd := "I_Reset_Button",
    Thermal_Protection := #Thermal_Status,
    Motor1_Out => "Q_Motor1",
    Motor2_Out => "Q_Motor2",
    Motor3_Out => "Q_Motor3", 
    Motor4_Out => "Q_Motor4",
    Motor5_Out => "Q_Motor5",
    System_Ready => "Q_System_Ready",
    System_Running => "Q_System_Running",
    Alarm => "Q_Alarm"
);

// =====================================================
// 网络3: 系统状态监控和诊断
// =====================================================

// 系统状态字更新
"Motor_Control_DB".System_Status.%X0 := "Q_System_Ready";
"Motor_Control_DB".System_Status.%X1 := "Q_System_Running"; 
"Motor_Control_DB".System_Status.%X2 := "Q_Alarm";
"Motor_Control_DB".System_Status.%X3 := "I_Emergency_Stop";
"Motor_Control_DB".System_Status.%X4 := #Thermal_Status;

// 电机状态字更新
"Motor_Control_DB".Motor_Status.%X0 := "Q_Motor1";
"Motor_Control_DB".Motor_Status.%X1 := "Q_Motor2";
"Motor_Control_DB".Motor_Status.%X2 := "Q_Motor3";
"Motor_Control_DB".Motor_Status.%X3 := "Q_Motor4";
"Motor_Control_DB".Motor_Status.%X4 := "Q_Motor5";

// 报警状态字更新
"Motor_Control_DB".Alarm_Status.%X0 := NOT "I_Emergency_Stop";
"Motor_Control_DB".Alarm_Status.%X1 := NOT "I_Motor1_Thermal";
"Motor_Control_DB".Alarm_Status.%X2 := NOT "I_Motor2_Thermal";
"Motor_Control_DB".Alarm_Status.%X3 := NOT "I_Motor3_Thermal";
"Motor_Control_DB".Alarm_Status.%X4 := NOT "I_Motor4_Thermal";
"Motor_Control_DB".Alarm_Status.%X5 := NOT "I_Motor5_Thermal";

// =====================================================
// 网络4: 计数器更新
// =====================================================

// 启动次数计数
IF "I_Start_Button" AND NOT "Motor_Control_DB".Motor_Sequence_Control.Start_Cmd THEN
    "Motor_Control_DB".Start_Counter := "Motor_Control_DB".Start_Counter + 1;
    "Motor_Control_DB".Last_Start_Time := DTL_TO_DATE_AND_TIME(RD_LOC_T());
END_IF;

// 紧急停止次数计数
IF NOT "I_Emergency_Stop" AND "Motor_Control_DB".Motor_Sequence_Control.Emergency_Stop THEN
    "Motor_Control_DB".Emergency_Stop_Counter := "Motor_Control_DB".Emergency_Stop_Counter + 1;
    "Motor_Control_DB".Last_Alarm_Time := DTL_TO_DATE_AND_TIME(RD_LOC_T());
END_IF;

// 热保护跳闸次数计数
IF NOT #Thermal_Status AND "Motor_Control_DB".Motor_Sequence_Control.Thermal_Protection THEN
    "Motor_Control_DB".Thermal_Trip_Counter := "Motor_Control_DB".Thermal_Trip_Counter + 1;
    "Motor_Control_DB".Last_Alarm_Time := DTL_TO_DATE_AND_TIME(RD_LOC_T());
END_IF;

// 停止时间记录
IF "I_Stop_Button" AND NOT "Motor_Control_DB".Motor_Sequence_Control.Stop_Cmd THEN
    "Motor_Control_DB".Last_Stop_Time := DTL_TO_DATE_AND_TIME(RD_LOC_T());
END_IF;

END_ORGANIZATION_BLOCK
