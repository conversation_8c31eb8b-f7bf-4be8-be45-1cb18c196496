<?xml version="1.0" encoding="UTF-8"?>
<Document>
  <Engineering version="V16" />
  <DocumentInfo>
    <Created>2025-01-29T00:00:00.0000000Z</Created>
    <ExportSetting>WithDefaults</ExportSetting>
  </DocumentInfo>
  <SW.Blocks.DB>
    <AttributeList>
      <Name>Motor_Control_DB</Name>
      <Number>1</Number>
      <ProgrammingLanguage>DB</ProgrammingLanguage>
      <Version>0.1</Version>
      <Comment>
        <MultiLanguageText Lang="zh-CN">电机控制数据块</MultiLanguageText>
      </Comment>
    </AttributeList>
    <ObjectList>
      <MultilingualText>
        <ObjectList>
          <MultilingualTextItem>
            <AttributeList>
              <Culture>zh-CN</Culture>
              <Text>电机顺序控制系统数据块
包含功能块FB1的实例数据</Text>
            </AttributeList>
          </MultilingualTextItem>
        </ObjectList>
      </MultilingualText>
      <SW.Blocks.BlockInterface>
        <AttributeList>
          <HeaderAuthor />
          <HeaderFamily />
          <HeaderName />
          <HeaderVersion>0.1</HeaderVersion>
        </AttributeList>
        <ObjectList>
          <SW.Blocks.BlockInterfaceSection>
            <AttributeList>
              <Name>Static</Name>
              <Section>Static</Section>
            </AttributeList>
            <ObjectList>
              <!-- FB1实例 -->
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Motor_Sequence_Control</Name>
                  <DataTypeName>FB1_Motor_Sequence_Control</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">电机顺序控制功能块实例</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <!-- 系统参数 -->
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Start_Delay_Time</Name>
                  <DataTypeName>Time</DataTypeName>
                  <StartValue>T#3s</StartValue>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">启动延时时间(3秒)</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Stop_Delay_Time</Name>
                  <DataTypeName>Time</DataTypeName>
                  <StartValue>T#5s</StartValue>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">停止延时时间(5秒)</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <!-- 状态监控 -->
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>System_Status</Name>
                  <DataTypeName>Word</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">系统状态字</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Motor_Status</Name>
                  <DataTypeName>Word</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">电机状态字</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Alarm_Status</Name>
                  <DataTypeName>Word</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">报警状态字</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <!-- 运行计数器 -->
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Start_Counter</Name>
                  <DataTypeName>DInt</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">启动次数计数器</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Emergency_Stop_Counter</Name>
                  <DataTypeName>DInt</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">紧急停止次数计数器</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Thermal_Trip_Counter</Name>
                  <DataTypeName>DInt</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">热保护跳闸次数计数器</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <!-- 运行时间统计 -->
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Motor1_Runtime</Name>
                  <DataTypeName>Time</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">电机1运行时间</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Motor2_Runtime</Name>
                  <DataTypeName>Time</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">电机2运行时间</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Motor3_Runtime</Name>
                  <DataTypeName>Time</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">电机3运行时间</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Motor4_Runtime</Name>
                  <DataTypeName>Time</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">电机4运行时间</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Motor5_Runtime</Name>
                  <DataTypeName>Time</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">电机5运行时间</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <!-- 诊断信息 -->
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Last_Start_Time</Name>
                  <DataTypeName>Date_And_Time</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">最后启动时间</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Last_Stop_Time</Name>
                  <DataTypeName>Date_And_Time</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">最后停止时间</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
              <SW.Blocks.BlockInterfaceEntry>
                <AttributeList>
                  <Name>Last_Alarm_Time</Name>
                  <DataTypeName>Date_And_Time</DataTypeName>
                  <Comment>
                    <MultiLanguageText Lang="zh-CN">最后报警时间</MultiLanguageText>
                  </Comment>
                </AttributeList>
              </SW.Blocks.BlockInterfaceEntry>
              
            </ObjectList>
          </SW.Blocks.BlockInterfaceSection>
        </ObjectList>
      </SW.Blocks.BlockInterface>
    </ObjectList>
  </SW.Blocks.DB>
</Document>
