# FB1功能块手动创建指南

## 🎯 创建FB1功能块步骤

### 步骤1：创建功能块
1. 右键"程序块" → "添加新块"
2. 块类型：功能块 (FB)
3. 名称：`FB1_Motor_Sequence_Control`
4. 编号：1
5. 编程语言：梯形图 (LAD)

### 步骤2：定义接口

#### Input参数
在接口编辑器中添加输入参数：
```
名称                数据类型    注释
Start_Cmd          Bool       启动命令
Stop_Cmd           Bool       停止命令
Emergency_Stop     Bool       紧急停止
Reset_Cmd          Bool       复位命令
Thermal_Protection Bool       热保护状态
```

#### Output参数
```
名称              数据类型    注释
Motor1_Out        Bool       电机1输出
Motor2_Out        Bool       电机2输出
Motor3_Out        Bool       电机3输出
Motor4_Out        Bool       电机4输出
Motor5_Out        Bool       电机5输出
System_Ready      Bool       系统就绪
System_Running    Bool       系统运行
Alarm             Bool       报警
```

#### Static参数
```
名称              数据类型      注释
Start_Timer_M1    TON_TIME     电机1启动定时器
Start_Timer_M2    TON_TIME     电机2启动定时器
Start_Timer_M3    TON_TIME     电机3启动定时器
Start_Timer_M4    TON_TIME     电机4启动定时器
Start_Timer_M5    TON_TIME     电机5启动定时器
Stop_Timer_M1     TON_TIME     电机1停止定时器
Stop_Timer_M2     TON_TIME     电机2停止定时器
Stop_Timer_M3     TON_TIME     电机3停止定时器
Stop_Timer_M4     TON_TIME     电机4停止定时器
Stop_Timer_M5     TON_TIME     电机5停止定时器
System_State      Int          系统状态
System_OK         Bool         系统正常状态
```

### 步骤3：编写梯形图逻辑

#### 网络1：系统安全检查
```
|--[Emergency_Stop]--[Thermal_Protection]---(System_OK)
```

#### 网络2：电机1启动控制（立即启动）
```
|--[Start_Cmd]--[System_OK]---(S)
|                              |
|--[Stop_Cmd]--+--[/Emergency_Stop]--+--[/Thermal_Protection]---(R)
|              |                     |                          |
|              +---------------------+                          |
+----------------------------------------------------------------(Motor1_Out)
```

#### 网络3：电机2启动控制（延时3秒）
```
|--[Motor1_Out]--[TON Start_Timer_M2, PT:=T#3s]--[Start_Timer_M2.Q]---(S)
|                                                                      |
|--[Stop_Cmd]--+--[/Emergency_Stop]--+--[/Thermal_Protection]---------(R)
|              |                     |                                |
|              +---------------------+                                |
+----------------------------------------------------------------------(Motor2_Out)
```

#### 网络4：电机3启动控制（延时6秒）
```
|--[Motor2_Out]--[TON Start_Timer_M3, PT:=T#3s]--[Start_Timer_M3.Q]---(S)
|                                                                      |
|--[Stop_Cmd]--+--[/Emergency_Stop]--+--[/Thermal_Protection]---------(R)
|              |                     |                                |
|              +---------------------+                                |
+----------------------------------------------------------------------(Motor3_Out)
```

#### 网络5：电机4启动控制（延时9秒）
```
|--[Motor3_Out]--[TON Start_Timer_M4, PT:=T#3s]--[Start_Timer_M4.Q]---(S)
|                                                                      |
|--[Stop_Cmd]--+--[/Emergency_Stop]--+--[/Thermal_Protection]---------(R)
|              |                     |                                |
|              +---------------------+                                |
+----------------------------------------------------------------------(Motor4_Out)
```

#### 网络6：电机5启动控制（延时12秒）
```
|--[Motor4_Out]--[TON Start_Timer_M5, PT:=T#3s]--[Start_Timer_M5.Q]---(S)
|                                                                      |
|--[Stop_Cmd]--+--[/Emergency_Stop]--+--[/Thermal_Protection]---------(R)
|              |                     |                                |
|              +---------------------+                                |
+----------------------------------------------------------------------(Motor5_Out)
```

#### 网络7：电机5停止控制（立即停止）
```
|--[Stop_Cmd]--+--[/Emergency_Stop]--+--[/Thermal_Protection]---(R)
|              |                     |                         |
|              +---------------------+                         |
+---------------------------------------------------------------(Motor5_Out)
```

#### 网络8：电机4停止控制（延时5秒）
```
|--[/Motor5_Out]--[Stop_Cmd]--[TON Stop_Timer_M4, PT:=T#5s]--[Stop_Timer_M4.Q]---(R)
|                                                                                  |
|--[/Emergency_Stop]--+--[/Thermal_Protection]-----------------------------------(R)
|                     |                                                          |
|                     +----------------------------------------------------------+
+--------------------------------------------------------------------------------(Motor4_Out)
```

#### 网络9：电机3停止控制（延时10秒）
```
|--[/Motor4_Out]--[Stop_Cmd]--[TON Stop_Timer_M3, PT:=T#5s]--[Stop_Timer_M3.Q]---(R)
|                                                                                  |
|--[/Emergency_Stop]--+--[/Thermal_Protection]-----------------------------------(R)
|                     |                                                          |
|                     +----------------------------------------------------------+
+--------------------------------------------------------------------------------(Motor3_Out)
```

#### 网络10：电机2停止控制（延时15秒）
```
|--[/Motor3_Out]--[Stop_Cmd]--[TON Stop_Timer_M2, PT:=T#5s]--[Stop_Timer_M2.Q]---(R)
|                                                                                  |
|--[/Emergency_Stop]--+--[/Thermal_Protection]-----------------------------------(R)
|                     |                                                          |
|                     +----------------------------------------------------------+
+--------------------------------------------------------------------------------(Motor2_Out)
```

#### 网络11：电机1停止控制（延时20秒）
```
|--[/Motor2_Out]--[Stop_Cmd]--[TON Stop_Timer_M1, PT:=T#5s]--[Stop_Timer_M1.Q]---(R)
|                                                                                  |
|--[/Emergency_Stop]--+--[/Thermal_Protection]-----------------------------------(R)
|                     |                                                          |
|                     +----------------------------------------------------------+
+--------------------------------------------------------------------------------(Motor1_Out)
```

#### 网络12：系统状态输出
```
|--[System_OK]--[/Motor1_Out]--[/Motor2_Out]--[/Motor3_Out]--[/Motor4_Out]--[/Motor5_Out]---(System_Ready)

|--[Motor1_Out]--+--[Motor2_Out]--+--[Motor3_Out]--+--[Motor4_Out]--+--[Motor5_Out]---(System_Running)
|                |                |                |                |
|                +----------------+----------------+----------------+

|--[/Emergency_Stop]--+--[/Thermal_Protection]---(Alarm)
|                     |
|                     +
```

## 🔧 编程技巧

### 梯形图元素说明：
- `[变量名]` = 常开触点
- `[/变量名]` = 常闭触点
- `(变量名)` = 线圈输出
- `(S)` = 置位线圈
- `(R)` = 复位线圈
- `TON` = 接通延时定时器

### 定时器使用：
- `PT` = 预设时间
- `T#3s` = 3秒时间常数
- `T#5s` = 5秒时间常数
- `.Q` = 定时器输出触点

## ✅ 完成检查

创建完成后检查：
- [ ] 接口参数全部定义
- [ ] 12个网络全部创建
- [ ] 定时器参数正确设置
- [ ] 逻辑关系正确连接
- [ ] 编译无错误

完成FB1后，下一步创建DB1数据块和OB1主程序！
