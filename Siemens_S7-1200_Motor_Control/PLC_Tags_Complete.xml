<?xml version="1.0" encoding="UTF-8"?>
<Document>
  <Engineering version="V16" />
  <DocumentInfo>
    <Created>2025-01-29T00:00:00.0000000Z</Created>
    <ExportSetting>WithDefaults</ExportSetting>
    <InstalledProducts>
      <Product>
        <DisplayName>Totally Integrated Automation Portal</DisplayName>
        <DisplayVersion>V16 Update 6</DisplayVersion>
      </Product>
    </InstalledProducts>
  </DocumentInfo>
  <SW.Tags.PlcTagTable>
    <AttributeList>
      <Name>PLC_Tags</Name>
    </AttributeList>
    <ObjectList>
      <!-- 输入变量 -->
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>I_Start_Button</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%I0.0</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">启动按钮</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>I_Stop_Button</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%I0.1</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">正常停止按钮</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>I_Emergency_Stop</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%I0.2</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">紧急停止按钮(常闭)</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>I_Motor1_Thermal</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%I0.3</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">电机1热保护(常闭)</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>I_Motor2_Thermal</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%I0.4</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">电机2热保护(常闭)</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>I_Motor3_Thermal</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%I0.5</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">电机3热保护(常闭)</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>I_Motor4_Thermal</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%I0.6</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">电机4热保护(常闭)</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>I_Motor5_Thermal</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%I0.7</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">电机5热保护(常闭)</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>I_Reset_Button</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%I1.0</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">复位按钮</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <!-- 输出变量 -->
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>Q_Motor1</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%Q0.0</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">电机1输出</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>Q_Motor2</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%Q0.1</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">电机2输出</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>Q_Motor3</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%Q0.2</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">电机3输出</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>Q_Motor4</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%Q0.3</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">电机4输出</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>Q_Motor5</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%Q0.4</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">电机5输出</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>Q_System_Ready</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%Q0.5</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">系统就绪指示灯</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>Q_System_Running</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%Q0.6</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">系统运行指示灯</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
      <SW.Tags.PlcTag>
        <AttributeList>
          <Name>Q_Alarm</Name>
          <DataTypeName>Bool</DataTypeName>
          <LogicalAddress>%Q0.7</LogicalAddress>
          <Comment>
            <MultiLanguageText Lang="zh-CN">报警指示灯</MultiLanguageText>
          </Comment>
        </AttributeList>
      </SW.Tags.PlcTag>
      
    </ObjectList>
  </SW.Tags.PlcTagTable>
</Document>
