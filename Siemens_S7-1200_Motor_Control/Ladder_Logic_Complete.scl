// =====================================================
// 西门子S7-1200 PLC 电机顺序控制程序
// 编程语言：梯形图 (LAD) 
// 开发环境：TIA Portal V16
// 功能：5台电机顺序启动/停止控制
// =====================================================

// 功能块 FB1: 电机顺序控制
FUNCTION_BLOCK "FB1_Motor_Sequence_Control"
{ S7_Optimized_Access := 'TRUE' }
VERSION : 0.1
   VAR_INPUT 
      Start_Cmd : Bool;   // 启动命令
      Stop_Cmd : Bool;    // 停止命令
      Emergency_Stop : Bool;   // 紧急停止
      Reset_Cmd : Bool;   // 复位命令
      Thermal_Protection : Bool;   // 热保护状态
   END_VAR

   VAR_OUTPUT 
      Motor1_Out : Bool;   // 电机1输出
      Motor2_Out : Bool;   // 电机2输出
      Motor3_Out : Bool;   // 电机3输出
      Motor4_Out : Bool;   // 电机4输出
      Motor5_Out : Bool;   // 电机5输出
      System_Ready : Bool;   // 系统就绪
      System_Running : Bool;   // 系统运行
      Alarm : Bool;   // 报警
   END_VAR

   VAR 
      Start_Timer_M1 : TON_TIME;   // 电机1启动定时器
      Start_Timer_M2 : TON_TIME;   // 电机2启动定时器
      Start_Timer_M3 : TON_TIME;   // 电机3启动定时器
      Start_Timer_M4 : TON_TIME;   // 电机4启动定时器
      Start_Timer_M5 : TON_TIME;   // 电机5启动定时器
      Stop_Timer_M5 : TON_TIME;    // 电机5停止定时器
      Stop_Timer_M4 : TON_TIME;    // 电机4停止定时器
      Stop_Timer_M3 : TON_TIME;    // 电机3停止定时器
      Stop_Timer_M2 : TON_TIME;    // 电机2停止定时器
      Stop_Timer_M1 : TON_TIME;    // 电机1停止定时器
      System_State : Int;          // 系统状态
      System_OK : Bool;            // 系统正常状态
      Start_Sequence : Bool;       // 启动序列
      Stop_Sequence : Bool;        // 停止序列
      Emergency_Active : Bool;     // 紧急停止激活
      Thermal_Trip : Bool;         // 热保护跳闸
   END_VAR

BEGIN
// =====================================================
// 网络1: 系统安全检查
// 检查紧急停止和热保护状态
// =====================================================
#System_OK := #Emergency_Stop AND #Thermal_Protection;
#Emergency_Active := NOT #Emergency_Stop;
#Thermal_Trip := NOT #Thermal_Protection;
#Alarm := #Emergency_Active OR #Thermal_Trip;

// =====================================================
// 网络2: 系统状态控制
// 0=停止, 1=启动中, 2=运行, 3=停止中
// =====================================================
IF #Emergency_Active OR #Thermal_Trip THEN
    #System_State := 0;  // 立即停止
    #Start_Sequence := FALSE;
    #Stop_Sequence := FALSE;
ELSIF #Start_Cmd AND #System_OK AND (#System_State = 0) THEN
    #System_State := 1;  // 启动序列
    #Start_Sequence := TRUE;
    #Stop_Sequence := FALSE;
ELSIF #Stop_Cmd AND (#System_State = 2) THEN
    #System_State := 3;  // 停止序列
    #Start_Sequence := FALSE;
    #Stop_Sequence := TRUE;
END_IF;

// =====================================================
// 网络3: 启动序列控制
// M1立即启动，其他电机延时3秒依次启动
// =====================================================

// 电机1 - 立即启动
IF #Start_Sequence AND #System_OK THEN
    #Motor1_Out := TRUE;
END_IF;

// 电机2 - 延时3秒启动
#Start_Timer_M2(IN := #Motor1_Out AND #Start_Sequence,
                PT := T#3s);
IF #Start_Timer_M2.Q THEN
    #Motor2_Out := TRUE;
END_IF;

// 电机3 - 延时6秒启动
#Start_Timer_M3(IN := #Motor2_Out AND #Start_Sequence,
                PT := T#3s);
IF #Start_Timer_M3.Q THEN
    #Motor3_Out := TRUE;
END_IF;

// 电机4 - 延时9秒启动
#Start_Timer_M4(IN := #Motor3_Out AND #Start_Sequence,
                PT := T#3s);
IF #Start_Timer_M4.Q THEN
    #Motor4_Out := TRUE;
END_IF;

// 电机5 - 延时12秒启动
#Start_Timer_M5(IN := #Motor4_Out AND #Start_Sequence,
                PT := T#3s);
IF #Start_Timer_M5.Q THEN
    #Motor5_Out := TRUE;
    #System_State := 2;  // 全部启动完成，进入运行状态
    #Start_Sequence := FALSE;
END_IF;

// =====================================================
// 网络4: 停止序列控制
// M5先停止，其他电机延时5秒依次停止
// =====================================================

// 电机5 - 立即停止
IF #Stop_Sequence THEN
    #Motor5_Out := FALSE;
END_IF;

// 电机4 - 延时5秒停止
#Stop_Timer_M4(IN := NOT #Motor5_Out AND #Stop_Sequence,
               PT := T#5s);
IF #Stop_Timer_M4.Q THEN
    #Motor4_Out := FALSE;
END_IF;

// 电机3 - 延时10秒停止
#Stop_Timer_M3(IN := NOT #Motor4_Out AND #Stop_Sequence,
               PT := T#5s);
IF #Stop_Timer_M3.Q THEN
    #Motor3_Out := FALSE;
END_IF;

// 电机2 - 延时15秒停止
#Stop_Timer_M2(IN := NOT #Motor3_Out AND #Stop_Sequence,
               PT := T#5s);
IF #Stop_Timer_M2.Q THEN
    #Motor2_Out := FALSE;
END_IF;

// 电机1 - 延时20秒停止
#Stop_Timer_M1(IN := NOT #Motor2_Out AND #Stop_Sequence,
               PT := T#5s);
IF #Stop_Timer_M1.Q THEN
    #Motor1_Out := FALSE;
    #System_State := 0;  // 全部停止完成
    #Stop_Sequence := FALSE;
END_IF;

// =====================================================
// 网络5: 紧急停止和热保护处理
// 立即停止所有电机
// =====================================================
IF #Emergency_Active OR #Thermal_Trip THEN
    #Motor1_Out := FALSE;
    #Motor2_Out := FALSE;
    #Motor3_Out := FALSE;
    #Motor4_Out := FALSE;
    #Motor5_Out := FALSE;
    // 复位所有定时器
    #Start_Timer_M1(IN := FALSE, PT := T#0s);
    #Start_Timer_M2(IN := FALSE, PT := T#0s);
    #Start_Timer_M3(IN := FALSE, PT := T#0s);
    #Start_Timer_M4(IN := FALSE, PT := T#0s);
    #Start_Timer_M5(IN := FALSE, PT := T#0s);
    #Stop_Timer_M1(IN := FALSE, PT := T#0s);
    #Stop_Timer_M2(IN := FALSE, PT := T#0s);
    #Stop_Timer_M3(IN := FALSE, PT := T#0s);
    #Stop_Timer_M4(IN := FALSE, PT := T#0s);
    #Stop_Timer_M5(IN := FALSE, PT := T#0s);
END_IF;

// =====================================================
// 网络6: 系统状态输出
// =====================================================
#System_Ready := #System_OK AND (#System_State = 0);
#System_Running := (#System_State = 1) OR (#System_State = 2) OR (#System_State = 3);

END_FUNCTION_BLOCK
