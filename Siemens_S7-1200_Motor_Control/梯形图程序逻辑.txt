=====================================================
西门子S7-1200 PLC 电机顺序控制 - 梯形图逻辑
适用于TIA Portal V16直接编程
=====================================================

功能块 FB1: 电机顺序控制
输入参数:
- Start_Cmd (Bool): 启动命令
- Stop_Cmd (Bool): 停止命令  
- Emergency_Stop (Bool): 紧急停止
- Reset_Cmd (Bool): 复位命令
- Thermal_Protection (Bool): 热保护状态

输出参数:
- Motor1_Out ~ Motor5_Out (Bool): 电机1-5输出
- System_Ready (Bool): 系统就绪
- System_Running (Bool): 系统运行
- Alarm (Bool): 报警

静态变量:
- Start_Timer_M1~M5 (TON_TIME): 启动定时器
- Stop_Timer_M1~M5 (TON_TIME): 停止定时器
- System_State (Int): 系统状态
- System_OK (Bool): 系统正常

=====================================================
网络1: 系统安全检查
=====================================================
|--[I_Emergency_Stop]--[I_Motor1_Thermal]--[I_Motor2_Thermal]--[I_Motor3_Thermal]--[I_Motor4_Thermal]--[I_Motor5_Thermal]---(System_OK)

=====================================================
网络2: 电机1启动控制 (立即启动)
=====================================================
|--[Start_Cmd]--[System_OK]--[/Emergency_Active]--[/Thermal_Trip]---(S)
|                                                                    |
|--[Stop_Cmd]--+--[Emergency_Active]--+--[Thermal_Trip]-----------(R)
|              |                      |                            |
|              +----------------------+                            |
+----------------------------------------------------------------(Motor1_Out)

=====================================================
网络3: 电机2启动控制 (延时3秒)
=====================================================
|--[Motor1_Out]--[System_OK]--[TON Start_Timer_M2, PT:=T#3s]--[Start_Timer_M2.Q]---(S)
|                                                                                    |
|--[Stop_Cmd]--+--[Emergency_Active]--+--[Thermal_Trip]----------------------------(R)
|              |                      |                                            |
|              +----------------------+                                            |
+--------------------------------------------------------------------------------(Motor2_Out)

=====================================================
网络4: 电机3启动控制 (延时6秒)
=====================================================
|--[Motor2_Out]--[System_OK]--[TON Start_Timer_M3, PT:=T#3s]--[Start_Timer_M3.Q]---(S)
|                                                                                    |
|--[Stop_Cmd]--+--[Emergency_Active]--+--[Thermal_Trip]----------------------------(R)
|              |                      |                                            |
|              +----------------------+                                            |
+--------------------------------------------------------------------------------(Motor3_Out)

=====================================================
网络5: 电机4启动控制 (延时9秒)
=====================================================
|--[Motor3_Out]--[System_OK]--[TON Start_Timer_M4, PT:=T#3s]--[Start_Timer_M4.Q]---(S)
|                                                                                    |
|--[Stop_Cmd]--+--[Emergency_Active]--+--[Thermal_Trip]----------------------------(R)
|              |                      |                                            |
|              +----------------------+                                            |
+--------------------------------------------------------------------------------(Motor4_Out)

=====================================================
网络6: 电机5启动控制 (延时12秒)
=====================================================
|--[Motor4_Out]--[System_OK]--[TON Start_Timer_M5, PT:=T#3s]--[Start_Timer_M5.Q]---(S)
|                                                                                    |
|--[Stop_Cmd]--+--[Emergency_Active]--+--[Thermal_Trip]----------------------------(R)
|              |                      |                                            |
|              +----------------------+                                            |
+--------------------------------------------------------------------------------(Motor5_Out)

=====================================================
网络7: 电机5停止控制 (立即停止)
=====================================================
|--[Stop_Cmd]--+--[Emergency_Active]--+--[Thermal_Trip]---(R)
|              |                      |                   |
|              +----------------------+                   |
+--------------------------------------------------------(Motor5_Out)

=====================================================
网络8: 电机4停止控制 (延时5秒)
=====================================================
|--[/Motor5_Out]--[Stop_Cmd]--[TON Stop_Timer_M4, PT:=T#5s]--[Stop_Timer_M4.Q]---(R)
|                                                                                  |
|--[Emergency_Active]--+--[Thermal_Trip]----------------------------------------------(R)
|                      |                                                          |
|                      +----------------------------------------------------------+
+--------------------------------------------------------------------------------(Motor4_Out)

=====================================================
网络9: 电机3停止控制 (延时10秒)
=====================================================
|--[/Motor4_Out]--[Stop_Cmd]--[TON Stop_Timer_M3, PT:=T#5s]--[Stop_Timer_M3.Q]---(R)
|                                                                                  |
|--[Emergency_Active]--+--[Thermal_Trip]----------------------------------------------(R)
|                      |                                                          |
|                      +----------------------------------------------------------+
+--------------------------------------------------------------------------------(Motor3_Out)

=====================================================
网络10: 电机2停止控制 (延时15秒)
=====================================================
|--[/Motor2_Out]--[Stop_Cmd]--[TON Stop_Timer_M2, PT:=T#5s]--[Stop_Timer_M2.Q]---(R)
|                                                                                  |
|--[Emergency_Active]--+--[Thermal_Trip]----------------------------------------------(R)
|                      |                                                          |
|                      +----------------------------------------------------------+
+--------------------------------------------------------------------------------(Motor2_Out)

=====================================================
网络11: 电机1停止控制 (延时20秒)
=====================================================
|--[/Motor2_Out]--[Stop_Cmd]--[TON Stop_Timer_M1, PT:=T#5s]--[Stop_Timer_M1.Q]---(R)
|                                                                                  |
|--[Emergency_Active]--+--[Thermal_Trip]----------------------------------------------(R)
|                      |                                                          |
|                      +----------------------------------------------------------+
+--------------------------------------------------------------------------------(Motor1_Out)

=====================================================
网络12: 系统状态输出
=====================================================
|--[System_OK]--[/Motor1_Out]--[/Motor2_Out]--[/Motor3_Out]--[/Motor4_Out]--[/Motor5_Out]---(System_Ready)

|--[Motor1_Out]--+--[Motor2_Out]--+--[Motor3_Out]--+--[Motor4_Out]--+--[Motor5_Out]---(System_Running)
|                |                |                |                |
|                +----------------+----------------+----------------+

|--[Emergency_Active]--+--[Thermal_Trip]---(Alarm)
|                      |
|                      +

=====================================================
主程序 OB1 调用示例:
=====================================================

网络1: 热保护状态合并
|--[I_Motor1_Thermal]--[I_Motor2_Thermal]--[I_Motor3_Thermal]--[I_Motor4_Thermal]--[I_Motor5_Thermal]---(M100.0)

网络2: 功能块调用
|--[CALL FB1_Motor_Sequence_Control, DB1_Motor_Control_Data]
   Start_Cmd := I_Start_Button
   Stop_Cmd := I_Stop_Button
   Emergency_Stop := I_Emergency_Stop
   Reset_Cmd := I_Reset_Button
   Thermal_Protection := M100.0
   Motor1_Out => Q_Motor1
   Motor2_Out => Q_Motor2
   Motor3_Out => Q_Motor3
   Motor4_Out => Q_Motor4
   Motor5_Out => Q_Motor5
   System_Ready => Q_System_Ready
   System_Running => Q_System_Running
   Alarm => Q_Alarm

=====================================================
使用说明:
1. 在TIA Portal中创建FB1功能块
2. 按照上述梯形图逻辑编程
3. 创建DB1数据块作为FB1实例
4. 在OB1中调用功能块
5. 配置变量表中的I/O地址
6. 编译下载到PLC

注意事项:
- 紧急停止和热保护为常闭触点
- 定时器时间可根据实际需要调整
- 建议添加手动/自动切换功能
- 实际应用时需要考虑电机保护和联锁
=====================================================
