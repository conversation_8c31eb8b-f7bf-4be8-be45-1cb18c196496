<?xml version="1.0" encoding="UTF-8"?>
<Document>
  <Engineering version="V16" />
  <DocumentInfo>
    <Created>2025-01-29T00:00:00.0000000Z</Created>
    <ExportSetting>WithDefaults</ExportSetting>
  </DocumentInfo>
  <SW.Blocks.FB>
    <AttributeList>
      <Name>FB1_Motor_Sequence_Control</Name>
      <Number>1</Number>
      <ProgrammingLanguage>LAD</ProgrammingLanguage>
      <Version>0.1</Version>
      <Comment>
        <MultiLanguageText Lang="zh-CN">电机顺序控制功能块</MultiLanguageText>
      </Comment>
    </AttributeList>
    <ObjectList>
      <MultilingualText>
        <ObjectList>
          <MultilingualTextItem>
            <AttributeList>
              <Culture>zh-CN</Culture>
              <Text>电机顺序控制功能块
功能：
1. 启动顺序：M1->M2->M3->M4->M5，间隔3秒
2. 停止顺序：M5->M4->M3->M2->M1，间隔5秒
3. 紧急停止立即停止所有电机
4. 热保护触发停止所有电机</Text>
            </AttributeList>
          </MultilingualTextItem>
        </ObjectList>
      </MultilingualText>
      <SW.Blocks.CompileUnit>
        <AttributeList>
          <NetworkSource>
            <FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">
              <Parts>
                <Access Scope="GlobalVariable" UId="21">
                  <Symbol>
                    <Component Name="I_Start_Button" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="22">
                  <Symbol>
                    <Component Name="I_Emergency_Stop" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="23">
                  <Symbol>
                    <Component Name="I_Motor1_Thermal" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="24">
                  <Symbol>
                    <Component Name="I_Motor2_Thermal" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="25">
                  <Symbol>
                    <Component Name="I_Motor3_Thermal" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="26">
                  <Symbol>
                    <Component Name="I_Motor4_Thermal" />
                  </Symbol>
                </Access>
                <Access Scope="GlobalVariable" UId="27">
                  <Symbol>
                    <Component Name="I_Motor5_Thermal" />
                  </Symbol>
                </Access>
                <Access Scope="LocalVariable" UId="28">
                  <Symbol>
                    <Component Name="System_OK" />
                  </Symbol>
                </Access>
                <Part Name="And" UId="29">
                  <Inputs>
                    <Input UId="30" />
                    <Input UId="31" />
                    <Input UId="32" />
                    <Input UId="33" />
                    <Input UId="34" />
                    <Input UId="35" />
                  </Inputs>
                  <Output UId="36" />
                </Part>
                <Part Name="Coil" UId="37">
                  <Input UId="38" />
                </Part>
              </Parts>
              <Wires>
                <Wire UId="39">
                  <Powerrail />
                  <NameCon UId="22" Name="in" />
                </Wire>
                <Wire UId="40">
                  <NameCon UId="22" Name="out" />
                  <NameCon UId="29" Name="in" UId="30" />
                </Wire>
                <Wire UId="41">
                  <NameCon UId="23" Name="out" />
                  <NameCon UId="29" Name="in" UId="31" />
                </Wire>
                <Wire UId="42">
                  <NameCon UId="24" Name="out" />
                  <NameCon UId="29" Name="in" UId="32" />
                </Wire>
                <Wire UId="43">
                  <NameCon UId="25" Name="out" />
                  <NameCon UId="29" Name="in" UId="33" />
                </Wire>
                <Wire UId="44">
                  <NameCon UId="26" Name="out" />
                  <NameCon UId="29" Name="in" UId="34" />
                </Wire>
                <Wire UId="45">
                  <NameCon UId="27" Name="out" />
                  <NameCon UId="29" Name="in" UId="35" />
                </Wire>
                <Wire UId="46">
                  <NameCon UId="29" Name="out" UId="36" />
                  <NameCon UId="37" Name="in" UId="38" />
                </Wire>
              </Wires>
            </FlgNet>
          </NetworkSource>
        </AttributeList>
        <ObjectList>
          <MultilingualText>
            <ObjectList>
              <MultilingualTextItem>
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text>网络1: 系统安全检查
检查紧急停止和所有电机热保护状态</Text>
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
        </ObjectList>
      </SW.Blocks.CompileUnit>

      <!-- 网络2: 启动序列控制 -->
      <SW.Blocks.CompileUnit>
        <AttributeList>
          <NetworkSource>
            <FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">
              <Parts>
                <!-- 电机1启动逻辑 -->
                <Access Scope="LocalVariable" UId="50">
                  <Symbol>
                    <Component Name="Start_Cmd" />
                  </Symbol>
                </Access>
                <Access Scope="LocalVariable" UId="51">
                  <Symbol>
                    <Component Name="System_OK" />
                  </Symbol>
                </Access>
                <Access Scope="LocalVariable" UId="52">
                  <Symbol>
                    <Component Name="Motor1_Out" />
                  </Symbol>
                </Access>
                <Part Name="And" UId="53">
                  <Inputs>
                    <Input UId="54" />
                    <Input UId="55" />
                  </Inputs>
                  <Output UId="56" />
                </Part>
                <Part Name="SR" UId="57">
                  <Inputs>
                    <Input Name="S1" UId="58" />
                    <Input Name="R" UId="59" />
                  </Inputs>
                  <Output Name="Q1" UId="60" />
                </Part>
                <Part Name="Coil" UId="61">
                  <Input UId="62" />
                </Part>
              </Parts>
              <Wires>
                <Wire UId="63">
                  <Powerrail />
                  <NameCon UId="50" Name="in" />
                </Wire>
                <Wire UId="64">
                  <NameCon UId="50" Name="out" />
                  <NameCon UId="53" Name="in" UId="54" />
                </Wire>
                <Wire UId="65">
                  <NameCon UId="51" Name="out" />
                  <NameCon UId="53" Name="in" UId="55" />
                </Wire>
                <Wire UId="66">
                  <NameCon UId="53" Name="out" UId="56" />
                  <NameCon UId="57" Name="in" UId="58" />
                </Wire>
                <Wire UId="67">
                  <NameCon UId="57" Name="out" UId="60" />
                  <NameCon UId="61" Name="in" UId="62" />
                </Wire>
              </Wires>
            </FlgNet>
          </NetworkSource>
        </AttributeList>
        <ObjectList>
          <MultilingualText>
            <ObjectList>
              <MultilingualTextItem>
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text>网络2: 电机1启动控制
启动命令且系统正常时立即启动电机1</Text>
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
        </ObjectList>
      </SW.Blocks.CompileUnit>

      <!-- 网络3: 电机2启动控制(延时3秒) -->
      <SW.Blocks.CompileUnit>
        <AttributeList>
          <NetworkSource>
            <FlgNet xmlns="http://www.siemens.com/automation/Openness/SW/NetworkSource/FlgNet/v4">
              <Parts>
                <Access Scope="LocalVariable" UId="70">
                  <Symbol>
                    <Component Name="Motor1_Out" />
                  </Symbol>
                </Access>
                <Access Scope="LocalVariable" UId="71">
                  <Symbol>
                    <Component Name="Start_Timer_M2" />
                  </Symbol>
                </Access>
                <Access Scope="LocalVariable" UId="72">
                  <Symbol>
                    <Component Name="Motor2_Out" />
                  </Symbol>
                </Access>
                <Part Name="TON" UId="73">
                  <Inputs>
                    <Input Name="IN" UId="74" />
                    <Input Name="PT" UId="75" />
                  </Inputs>
                  <Outputs>
                    <Output Name="Q" UId="76" />
                    <Output Name="ET" UId="77" />
                  </Outputs>
                </Part>
                <Part Name="Coil" UId="78">
                  <Input UId="79" />
                </Part>
                <Const Name="T#3s" UId="80" />
              </Parts>
              <Wires>
                <Wire UId="81">
                  <Powerrail />
                  <NameCon UId="70" Name="in" />
                </Wire>
                <Wire UId="82">
                  <NameCon UId="70" Name="out" />
                  <NameCon UId="73" Name="in" UId="74" />
                </Wire>
                <Wire UId="83">
                  <NameCon UId="80" Name="out" />
                  <NameCon UId="73" Name="in" UId="75" />
                </Wire>
                <Wire UId="84">
                  <NameCon UId="73" Name="out" UId="76" />
                  <NameCon UId="78" Name="in" UId="79" />
                </Wire>
              </Wires>
            </FlgNet>
          </NetworkSource>
        </AttributeList>
        <ObjectList>
          <MultilingualText>
            <ObjectList>
              <MultilingualTextItem>
                <AttributeList>
                  <Culture>zh-CN</Culture>
                  <Text>网络3: 电机2启动控制
电机1启动后延时3秒启动电机2</Text>
                </AttributeList>
              </MultilingualTextItem>
            </ObjectList>
          </MultilingualText>
        </ObjectList>
      </SW.Blocks.CompileUnit>

    </ObjectList>
    <SW.Blocks.BlockInterface>
      <AttributeList>
        <HeaderAuthor />
        <HeaderFamily />
        <HeaderName />
        <HeaderVersion>0.1</HeaderVersion>
      </AttributeList>
      <ObjectList>
        <SW.Blocks.BlockInterfaceSection>
          <AttributeList>
            <Name>Input</Name>
            <Section>Input</Section>
          </AttributeList>
          <ObjectList>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Start_Cmd</Name>
                <DataTypeName>Bool</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">启动命令</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Stop_Cmd</Name>
                <DataTypeName>Bool</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">停止命令</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Emergency_Stop</Name>
                <DataTypeName>Bool</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">紧急停止</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Reset_Cmd</Name>
                <DataTypeName>Bool</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">复位命令</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Thermal_Protection</Name>
                <DataTypeName>Bool</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">热保护状态</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
          </ObjectList>
        </SW.Blocks.BlockInterfaceSection>
        <SW.Blocks.BlockInterfaceSection>
          <AttributeList>
            <Name>Output</Name>
            <Section>Output</Section>
          </AttributeList>
          <ObjectList>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Motor1_Out</Name>
                <DataTypeName>Bool</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机1输出</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Motor2_Out</Name>
                <DataTypeName>Bool</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机2输出</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Motor3_Out</Name>
                <DataTypeName>Bool</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机3输出</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Motor4_Out</Name>
                <DataTypeName>Bool</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机4输出</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Motor5_Out</Name>
                <DataTypeName>Bool</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机5输出</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>System_Ready</Name>
                <DataTypeName>Bool</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">系统就绪</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>System_Running</Name>
                <DataTypeName>Bool</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">系统运行</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Alarm</Name>
                <DataTypeName>Bool</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">报警</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
          </ObjectList>
        </SW.Blocks.BlockInterfaceSection>
        <SW.Blocks.BlockInterfaceSection>
          <AttributeList>
            <Name>Static</Name>
            <Section>Static</Section>
          </AttributeList>
          <ObjectList>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Start_Timer_M1</Name>
                <DataTypeName>TON_TIME</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机1启动定时器</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Start_Timer_M2</Name>
                <DataTypeName>TON_TIME</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机2启动定时器</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Start_Timer_M3</Name>
                <DataTypeName>TON_TIME</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机3启动定时器</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Start_Timer_M4</Name>
                <DataTypeName>TON_TIME</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机4启动定时器</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Start_Timer_M5</Name>
                <DataTypeName>TON_TIME</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机5启动定时器</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Stop_Timer_M5</Name>
                <DataTypeName>TON_TIME</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机5停止定时器</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Stop_Timer_M4</Name>
                <DataTypeName>TON_TIME</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机4停止定时器</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Stop_Timer_M3</Name>
                <DataTypeName>TON_TIME</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机3停止定时器</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Stop_Timer_M2</Name>
                <DataTypeName>TON_TIME</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机2停止定时器</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>Stop_Timer_M1</Name>
                <DataTypeName>TON_TIME</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">电机1停止定时器</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
            <SW.Blocks.BlockInterfaceEntry>
              <AttributeList>
                <Name>System_State</Name>
                <DataTypeName>Int</DataTypeName>
                <Comment>
                  <MultiLanguageText Lang="zh-CN">系统状态 0=停止 1=启动中 2=运行 3=停止中</MultiLanguageText>
                </Comment>
              </AttributeList>
            </SW.Blocks.BlockInterfaceEntry>
          </ObjectList>
        </SW.Blocks.BlockInterfaceSection>
      </ObjectList>
    </SW.Blocks.BlockInterface>
  </SW.Blocks.FB>
</Document>
