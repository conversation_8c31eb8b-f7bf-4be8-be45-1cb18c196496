# TIA Portal V16 Excel变量表模板制作指南

## 🎯 正确的Excel格式要求

根据您提供的图片，需要严格按照以下格式创建Excel文件：

### 📊 表头格式 (第1行)
```
A1: Name
B1: Path  
C1: Data Type
D1: Logical Address
E1: Comment
F1: Hmi Visible
G1: Hmi Accessible  
H1: Hmi Writeable
I1: Typeobject ID
J1: Version ID
```

## 📋 手动创建Excel文件步骤

### 步骤1：打开Excel
1. 打开Microsoft Excel
2. 创建新的空白工作簿

### 步骤2：输入表头 (第1行)
在第1行依次输入：
- **A1**: Name
- **B1**: Path
- **C1**: Data Type  
- **D1**: Logical Address
- **E1**: Comment
- **F1**: Hmi Visible
- **G1**: Hmi Accessible
- **H1**: Hmi Writeable
- **I1**: Typeobject ID
- **J1**: Version ID

### 步骤3：输入变量数据 (第2-18行)

#### 输入变量 (第2-10行)
| 行 | A列(Name) | B列(Path) | C列(Data Type) | D列(Address) | E列(Comment) | F列 | G列 | H列 | I列 | J列 |
|----|-----------|-----------|----------------|--------------|--------------|-----|-----|-----|-----|-----|
| 2  | I_Start_Button | (空) | Bool | %I0.0 | 启动按钮 | True | True | False | (空) | (空) |
| 3  | I_Stop_Button | (空) | Bool | %I0.1 | 正常停止按钮 | True | True | False | (空) | (空) |
| 4  | I_Emergency_Stop | (空) | Bool | %I0.2 | 紧急停止按钮(常闭) | True | True | False | (空) | (空) |
| 5  | I_Motor1_Thermal | (空) | Bool | %I0.3 | 电机1热保护(常闭) | True | True | False | (空) | (空) |
| 6  | I_Motor2_Thermal | (空) | Bool | %I0.4 | 电机2热保护(常闭) | True | True | False | (空) | (空) |
| 7  | I_Motor3_Thermal | (空) | Bool | %I0.5 | 电机3热保护(常闭) | True | True | False | (空) | (空) |
| 8  | I_Motor4_Thermal | (空) | Bool | %I0.6 | 电机4热保护(常闭) | True | True | False | (空) | (空) |
| 9  | I_Motor5_Thermal | (空) | Bool | %I0.7 | 电机5热保护(常闭) | True | True | False | (空) | (空) |
| 10 | I_Reset_Button | (空) | Bool | %I1.0 | 复位按钮 | True | True | False | (空) | (空) |

#### 输出变量 (第11-18行)
| 行 | A列(Name) | B列(Path) | C列(Data Type) | D列(Address) | E列(Comment) | F列 | G列 | H列 | I列 | J列 |
|----|-----------|-----------|----------------|--------------|--------------|-----|-----|-----|-----|-----|
| 11 | Q_Motor1 | (空) | Bool | %Q0.0 | 电机1输出 | True | True | False | (空) | (空) |
| 12 | Q_Motor2 | (空) | Bool | %Q0.1 | 电机2输出 | True | True | False | (空) | (空) |
| 13 | Q_Motor3 | (空) | Bool | %Q0.2 | 电机3输出 | True | True | False | (空) | (空) |
| 14 | Q_Motor4 | (空) | Bool | %Q0.3 | 电机4输出 | True | True | False | (空) | (空) |
| 15 | Q_Motor5 | (空) | Bool | %Q0.4 | 电机5输出 | True | True | False | (空) | (空) |
| 16 | Q_System_Ready | (空) | Bool | %Q0.5 | 系统就绪指示灯 | True | True | False | (空) | (空) |
| 17 | Q_System_Running | (空) | Bool | %Q0.6 | 系统运行指示灯 | True | True | False | (空) | (空) |
| 18 | Q_Alarm | (空) | Bool | %Q0.7 | 报警指示灯 | True | True | False | (空) | (空) |

### 步骤4：保存文件
1. 文件 → 另存为
2. 文件名：`PLC_Tags_Manual.xlsx`
3. 文件类型：Excel工作簿 (*.xlsx)
4. 保存

## 🔧 快速复制粘贴方法

### 方法1：使用我提供的CSV文件
1. 用Excel打开 `PLC_Tags_Excel_Format.csv`
2. 数据会自动分列到正确位置
3. 另存为 `.xlsx` 格式

### 方法2：直接复制粘贴
复制以下内容到Excel中：

```
Name	Path	Data Type	Logical Address	Comment	Hmi Visible	Hmi Accessible	Hmi Writeable	Typeobject ID	Version ID
I_Start_Button		Bool	%I0.0	启动按钮	True	True	False		
I_Stop_Button		Bool	%I0.1	正常停止按钮	True	True	False		
I_Emergency_Stop		Bool	%I0.2	紧急停止按钮(常闭)	True	True	False		
I_Motor1_Thermal		Bool	%I0.3	电机1热保护(常闭)	True	True	False		
I_Motor2_Thermal		Bool	%I0.4	电机2热保护(常闭)	True	True	False		
I_Motor3_Thermal		Bool	%I0.5	电机3热保护(常闭)	True	True	False		
I_Motor4_Thermal		Bool	%I0.6	电机4热保护(常闭)	True	True	False		
I_Motor5_Thermal		Bool	%I0.7	电机5热保护(常闭)	True	True	False		
I_Reset_Button		Bool	%I1.0	复位按钮	True	True	False		
Q_Motor1		Bool	%Q0.0	电机1输出	True	True	False		
Q_Motor2		Bool	%Q0.1	电机2输出	True	True	False		
Q_Motor3		Bool	%Q0.2	电机3输出	True	True	False		
Q_Motor4		Bool	%Q0.3	电机4输出	True	True	False		
Q_Motor5		Bool	%Q0.4	电机5输出	True	True	False		
Q_System_Ready		Bool	%Q0.5	系统就绪指示灯	True	True	False		
Q_System_Running		Bool	%Q0.6	系统运行指示灯	True	True	False		
Q_Alarm		Bool	%Q0.7	报警指示灯	True	True	False		
```

## ✅ 验证Excel格式

创建完成后，检查：
- [ ] 表头在第1行，共10列 (A-J)
- [ ] 数据从第2行开始，共17行变量
- [ ] B列(Path)、I列、J列为空
- [ ] 所有数据对齐到正确的列
- [ ] 文件保存为 `.xlsx` 格式

## 🚀 导入到TIA Portal

Excel文件创建完成后：
1. TIA Portal → PLC变量表 → 右键 → "从文件导入"
2. 选择您创建的 `.xlsx` 文件
3. 确认列映射正确
4. 完成导入

这样创建的Excel文件应该能够成功导入到TIA Portal V16中！
