# 电机控制系统变量检查清单

## 🎯 需要的变量清单

请在您的默认变量表中检查以下17个变量是否存在：

### 📥 输入变量（9个）

| 序号 | 变量名 | 数据类型 | 地址 | 注释 | 是否存在 |
|------|--------|----------|------|------|----------|
| 1 | I_Start_Button | Bool | %I0.0 | 启动按钮 | ☐ |
| 2 | I_Stop_Button | Bool | %I0.1 | 正常停止按钮 | ☐ |
| 3 | I_Emergency_Stop | Bool | %I0.2 | 紧急停止按钮(常闭) | ☐ |
| 4 | I_Motor1_Thermal | Bool | %I0.3 | 电机1热保护(常闭) | ☐ |
| 5 | I_Motor2_Thermal | Bool | %I0.4 | 电机2热保护(常闭) | ☐ |
| 6 | I_Motor3_Thermal | Bool | %I0.5 | 电机3热保护(常闭) | ☐ |
| 7 | I_Motor4_Thermal | Bool | %I0.6 | 电机4热保护(常闭) | ☐ |
| 8 | I_Motor5_Thermal | Bool | %I0.7 | 电机5热保护(常闭) | ☐ |
| 9 | I_Reset_Button | Bool | %I1.0 | 复位按钮 | ☐ |

### 📤 输出变量（8个）

| 序号 | 变量名 | 数据类型 | 地址 | 注释 | 是否存在 |
|------|--------|----------|------|------|----------|
| 10 | Q_Motor1 | Bool | %Q0.0 | 电机1输出 | ☐ |
| 11 | Q_Motor2 | Bool | %Q0.1 | 电机2输出 | ☐ |
| 12 | Q_Motor3 | Bool | %Q0.2 | 电机3输出 | ☐ |
| 13 | Q_Motor4 | Bool | %Q0.3 | 电机4输出 | ☐ |
| 14 | Q_Motor5 | Bool | %Q0.4 | 电机5输出 | ☐ |
| 15 | Q_System_Ready | Bool | %Q0.5 | 系统就绪指示灯 | ☐ |
| 16 | Q_System_Running | Bool | %Q0.6 | 系统运行指示灯 | ☐ |
| 17 | Q_Alarm | Bool | %Q0.7 | 报警指示灯 | ☐ |

## 🔧 操作步骤

### 步骤1：检查现有变量
1. 在默认变量表中逐一查找上述17个变量
2. 在"是否存在"列打勾标记已存在的变量
3. 记录缺失的变量

### 步骤2：检查地址冲突
查看以下地址是否被其他变量占用：
- **输入地址**: %I0.0, %I0.1, %I0.2, %I0.3, %I0.4, %I0.5, %I0.6, %I0.7, %I1.0
- **输出地址**: %Q0.0, %Q0.1, %Q0.2, %Q0.3, %Q0.4, %Q0.5, %Q0.6, %Q0.7

### 步骤3：手动添加缺失变量
对于不存在的变量，手动添加：
1. 在变量表最后一行点击
2. 输入变量名
3. 选择数据类型：Bool
4. 输入逻辑地址
5. 添加注释

## 🚨 常见问题解决

### 问题1：地址冲突
**现象**: 提示地址已被使用
**解决**: 
- 检查冲突的变量是否可以删除
- 或者修改我们的变量地址（如使用%I2.0开始）

### 问题2：变量名重复
**现象**: 提示变量名已存在
**解决**:
- 检查现有变量是否就是我们需要的
- 或者使用不同的变量名（如Motor_Start_Button）

### 问题3：数据类型错误
**现象**: 变量创建失败
**解决**:
- 确保数据类型输入为"Bool"（注意大小写）
- 检查地址格式是否正确（必须包含%符号）

## 📋 快速添加模板

如果需要手动添加，可以复制以下内容：

```
变量名: I_Start_Button
数据类型: Bool
地址: %I0.0
注释: 启动按钮

变量名: I_Stop_Button
数据类型: Bool
地址: %I0.1
注释: 正常停止按钮

变量名: I_Emergency_Stop
数据类型: Bool
地址: %I0.2
注释: 紧急停止按钮(常闭)

变量名: I_Motor1_Thermal
数据类型: Bool
地址: %I0.3
注释: 电机1热保护(常闭)

变量名: I_Motor2_Thermal
数据类型: Bool
地址: %I0.4
注释: 电机2热保护(常闭)

变量名: I_Motor3_Thermal
数据类型: Bool
地址: %I0.5
注释: 电机3热保护(常闭)

变量名: I_Motor4_Thermal
数据类型: Bool
地址: %I0.6
注释: 电机4热保护(常闭)

变量名: I_Motor5_Thermal
数据类型: Bool
地址: %I0.7
注释: 电机5热保护(常闭)

变量名: I_Reset_Button
数据类型: Bool
地址: %I1.0
注释: 复位按钮

变量名: Q_Motor1
数据类型: Bool
地址: %Q0.0
注释: 电机1输出

变量名: Q_Motor2
数据类型: Bool
地址: %Q0.1
注释: 电机2输出

变量名: Q_Motor3
数据类型: Bool
地址: %Q0.2
注释: 电机3输出

变量名: Q_Motor4
数据类型: Bool
地址: %Q0.3
注释: 电机4输出

变量名: Q_Motor5
数据类型: Bool
地址: %Q0.4
注释: 电机5输出

变量名: Q_System_Ready
数据类型: Bool
地址: %Q0.5
注释: 系统就绪指示灯

变量名: Q_System_Running
数据类型: Bool
地址: %Q0.6
注释: 系统运行指示灯

变量名: Q_Alarm
数据类型: Bool
地址: %Q0.7
注释: 报警指示灯
```

## ✅ 完成验证

添加完成后，变量表应该包含：
- 总变量数: 29 + 17 = 46个（如果全部是新变量）
- 或者仍然是29个（如果部分变量已存在）

请按照这个清单检查您的变量表，告诉我哪些变量缺失，我可以提供更具体的帮助！
