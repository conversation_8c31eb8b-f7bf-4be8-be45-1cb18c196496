# 西门子S7-1200 PLC 电机顺序控制程序说明

## 项目概述
- **项目名称**: 5台电机顺序启动/停止控制系统
- **PLC型号**: 西门子S7-1200系列
- **编程环境**: TIA Portal V16
- **编程语言**: 梯形图 (LAD) / 结构化控制语言 (SCL)
- **版本**: V1.0
- **创建日期**: 2025-01-29

## 功能需求

### A. 安全功能
1. **紧急停止按钮** - 立即停止所有电机，优先级最高
2. **热保护功能** - 任何电机热保护触发都会停止所有电机
3. **正常停止按钮** - 触发顺序停止流程

### B. 启动顺序
1. 按下启动按钮，电机1立即启动
2. 每间隔3秒依次启动下一台电机
3. 启动顺序：M1 → M2 → M3 → M4 → M5

### C. 停止顺序
1. 按下停止按钮，从最后一台电机开始停止
2. 每间隔5秒停止前一台电机
3. 停止顺序：M5 → M4 → M3 → M2 → M1

### D. 保护功能
1. 任何热保护触发会立即停止所有电机
2. 需要复位热保护后才能重新启动
3. 紧急停止优先级最高

## 硬件配置

### 输入信号 (I)
| 地址 | 信号名称 | 描述 | 类型 |
|------|----------|------|------|
| I0.0 | I_Start_Button | 启动按钮 | 常开 |
| I0.1 | I_Stop_Button | 正常停止按钮 | 常开 |
| I0.2 | I_Emergency_Stop | 紧急停止按钮 | 常闭 |
| I0.3 | I_Motor1_Thermal | 电机1热保护 | 常闭 |
| I0.4 | I_Motor2_Thermal | 电机2热保护 | 常闭 |
| I0.5 | I_Motor3_Thermal | 电机3热保护 | 常闭 |
| I0.6 | I_Motor4_Thermal | 电机4热保护 | 常闭 |
| I0.7 | I_Motor5_Thermal | 电机5热保护 | 常闭 |
| I1.0 | I_Reset_Button | 复位按钮 | 常开 |

### 输出信号 (Q)
| 地址 | 信号名称 | 描述 |
|------|----------|------|
| Q0.0 | Q_Motor1 | 电机1输出 |
| Q0.1 | Q_Motor2 | 电机2输出 |
| Q0.2 | Q_Motor3 | 电机3输出 |
| Q0.3 | Q_Motor4 | 电机4输出 |
| Q0.4 | Q_Motor5 | 电机5输出 |
| Q0.5 | Q_System_Ready | 系统就绪指示灯 |
| Q0.6 | Q_System_Running | 系统运行指示灯 |
| Q0.7 | Q_Alarm | 报警指示灯 |

## 程序结构

### 主要程序块
1. **OB1 (Main)** - 主程序循环
2. **FB1_Motor_Sequence_Control** - 电机顺序控制功能块
3. **DB1_Motor_Control_Data** - 数据块

### 程序文件列表
- `Variables.xml` - 变量表定义
- `FB1_Motor_Sequence_Control.xml` - 功能块(梯形图)
- `OB1_Main.xml` - 主程序(梯形图)
- `DB1_Motor_Control_Data.xml` - 数据块
- `Ladder_Logic_Complete.scl` - 完整SCL代码
- `OB1_Main_Program.scl` - 主程序SCL代码

## 控制逻辑详解

### 系统状态机
```
状态0: 停止状态
状态1: 启动序列中
状态2: 正常运行
状态3: 停止序列中
```

### 启动时序图
```
时间轴: 0s    3s    6s    9s    12s
电机1:  ■■■■■■■■■■■■■■■■■
电机2:        ■■■■■■■■■■■■■
电机3:              ■■■■■■■■■
电机4:                    ■■■■■
电机5:                          ■
```

### 停止时序图
```
时间轴: 0s    5s    10s   15s   20s
电机5:  □
电机4:        □
电机3:              □
电机2:                    □
电机1:                          □
```

## 安全特性

### 优先级设计
1. **最高优先级**: 紧急停止
2. **高优先级**: 热保护
3. **正常优先级**: 启动/停止命令

### 故障处理
- 紧急停止或热保护触发时，立即停止所有电机
- 复位所有定时器，清除启动/停止序列
- 设置报警状态，记录故障时间

## 监控和诊断

### 状态监控
- 系统状态字：显示当前系统状态
- 电机状态字：显示各电机运行状态
- 报警状态字：显示各种报警状态

### 统计功能
- 启动次数计数器
- 紧急停止次数计数器
- 热保护跳闸次数计数器
- 各电机运行时间统计

### 时间记录
- 最后启动时间
- 最后停止时间
- 最后报警时间

## 使用说明

### 导入步骤
1. 打开TIA Portal V16
2. 创建新项目，选择S7-1200 PLC
3. 导入变量表文件 `Variables.xml`
4. 导入功能块 `FB1_Motor_Sequence_Control.xml`
5. 导入数据块 `DB1_Motor_Control_Data.xml`
6. 导入主程序 `OB1_Main.xml`
7. 编译并下载到PLC

### 操作流程
1. **系统上电**: 检查所有指示灯状态
2. **启动操作**: 
   - 确认系统就绪灯亮起
   - 按下启动按钮
   - 观察电机按序启动
3. **停止操作**:
   - 按下停止按钮
   - 观察电机按序停止
4. **紧急情况**:
   - 按下紧急停止按钮
   - 所有电机立即停止
   - 按复位按钮清除报警

## 维护和故障排除

### 常见故障
1. **电机不启动**: 检查热保护状态、紧急停止状态
2. **启动顺序错误**: 检查定时器设置
3. **无法停止**: 检查停止按钮、程序逻辑

### 维护建议
1. 定期检查热保护器工作状态
2. 测试紧急停止功能
3. 检查电机运行时间统计
4. 备份程序和参数

## 技术支持
如有技术问题，请联系系统集成商或参考TIA Portal帮助文档。

---
**注意**: 本程序仅供参考，实际应用时请根据具体设备和安全要求进行调整。
