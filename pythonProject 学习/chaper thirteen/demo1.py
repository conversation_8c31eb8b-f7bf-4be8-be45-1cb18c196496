# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/23 15:04
"""面向对象的三大特征1.封装：提高程序的安全性 a.将数据（属性）和行为（方法)包装到类对象中。在方法内部对属性进行操作，在类对象的外部调用方法。
这样，无需关心方法内部的具体实现细节，从而隔离了复杂度；  b.在Python中没有专门的修饰符用于属性的私有，如果该属性不希望在类对象外部被访问，
前面使用两个"_".
2.继承：提高代码的复用性               3.多态：提高程序的可扩展性和可维护性"""


class Car:

    def __init__(self, brand):           # brand 为一个实例属性  初始化

        self.brand = brand               # 调用自己 待验证

    def start(self):
        print('汽车已启动，，，')


car = Car('宝马X5')                   # 创建一个实例对象
car.start()                          # 调用实例方法
print(car.brand)                     # 输出汽车品牌

# 不希望在类的外部使用，所以加了两个_


class Student:
    def __init__(self, name, age):    # 定义了一个init方法
        self.name = name              # name age 是实例属性
        self.__age = age              # 年龄不希望在类的外部使用，所以加了两个_

    def show(self):                   # 定义一个show实例方法，在类的内部使用age
        print(self.name, self.__age)


stu = Student('张三', 20)              # 创建实例对象
stu.show()                            # 调用实例方法
# 在类的外部强制使用 name,age实例属性
print(stu.name)
# print(stu.__age)
print(dir(stu))            # dir内置函数查询stu 全部属性和方法
# print(stu._Student__age)   # 强制查看age

# 封装的实现


class Student1:
    def __init__(self, age0):
        self.set_age0(age0)

    def get_age0(self):
        return self.__age0

    def set_age0(self, age0):
        if 0 <= age0 <= 120:

            self.__age0 = age0
        else:
            self.__age0 = 18


stu1 = Student1(150)
stu2 = Student1(30)
print(stu1.get_age0())
print(stu2.get_age0())

print("""继承------>语法格式class 子类类名（父类1，父类2...):
                                Pass""")
# 如果一个类没有继承任何类，则默认继承object   python 支持多继承
# 定义子类时，必须在其构造函数中调用父类的构造函数


class Person(object):         # person 继承object类
    def __init__(self, name, age):
        self.name = name
        self.age = age

    def info(self):
        print(self.name, self.age)


class Student3(Person):
    def __init__(self, name2, age2, stu2_no):
        super().__init__(name2, age2)             # super()是一个调用父类的方法，直接用类名调用父类的方法在单继承中是没有问题的；
        self.stu2_no = stu2_no                    # 但是如果使用多继承会涉及到查找顺序（mro),重复调用等种种问题

    def info(self):                             # 对父类的方法不满意，进行重写
        super().info()                          # 程序执行到这一步时，调用父类的实例方法
        print(self.stu2_no)


class Teacher3(Person):                          # 用花括号{}占位符来实现 format() 格式化方法
    def __init__(self, name3, age3, teachofyear):
        super().__init__(name3, age3)
        self.teachofyear = teachofyear

    def info(self):
        super().info()                          # 程序执行到这一步时，调用父类的实例方法
        print('教龄', self.teachofyear)


stu3 = Student3('张三', 20, '1001')
teacher3 = Teacher3('李四', 34, 10)

stu3.info()
print('--------------------------------------------')
teacher3.info()


# 多继承
class A(object):     # object所有对象的父类
    pass


class B(object):     # object所有对象的父类
    pass


class C(A, B):       # 同时继承AB
    pass


print("""object 类是所有类的父类，""")       # 因此，所有的类都有object类的属性和方法，内置函数dir（）可查看指定对象所有属性
# object 有一个_str_()方法，用于返回一个对于’对象的描述‘，对应于内置函数str（）经常用于str（）方法，帮我们查看对象的信息
# 所以我们经常会对str（）进行重写


class Student4:
    def __init__(self, name4, age4):
        self.name4 = name4
        self.age4 = age4

    def __str__(self):
        return '我的名字是{0}，今年{1}岁'. format(self.name4, self.age4)


stu4 = Student4("赵洋", 20)
print(dir(stu4))

print(stu4)
print(type(stu4))

"""多态---简单的讲，就是具有多种形态，它指的是：即便不知道一个变量所引用的对象到底是什么类型，仍然可以通过这个变量的调用方法
在运行过程中，根据变量所引用对象的类型，动态决定调用那个对象中的方法"""


class Animal(object):

    def eat(self):
        print('动物会吃')


class Dog(Animal):
    def eat(self):
        print('狗吃骨头')


class Cat(Animal):
    def eat(self):
        print('猫吃鱼')


class Person2:
    def eat(self):
        print('人吃五谷杂粮')


# 定义一个函数
def fun(obj):
    obj.eat()


# 开始调用函数
fun(Cat())
fun(Dog())
fun(Animal())
print('------------------------------')
fun(Person2())


print("""-----------静态语言与动态语言------------""")
# 静态语言与动态语言关于多态的区别-----静态语言实现多态的三个必要条件 1.继承 2.方法重写 3.父类引用指向子类对象
# 动态语言实物多态崇尚‘鸭子类型’，当看到一只鸟走起来像鸭子，游泳起来像鸭子，飞起来也像鸭子，那么这只鸟就可以被称为鸭子。
# 在鸭子的类型中，不需要关心对象是什么类型，到底是不是鸭子，只关心对象的行为。

# 特殊方法和特殊属性
# 特殊属性_dict_  获得类对象或实例对象所绑定的所有属性和方法的字典
# 特殊方法 _len_()   通过重写_len_() 方法，让内置函数len()的参数可以是自定义类型
# 特殊方法 _add_()   通过重写_add_() 方法，可使用自定义对象具有‘+’功能
# 特殊方法 _new_()   用于创建对象
# 特殊方法 _init_()  对创建的对象进行初始化


class A1:     # object所有对象的父类
    pass


class B1:     # object所有对象的父类
    pass


class C1(A1, B1):       # 同时继承AB

    def __init__(self, name5, age5):
        self.name5 = name5
        self.age5 = age5

# 创建C类的对象


x = C1('Jack', 20)   # x是C类型的一个实例对象
print(x.__dict__)    # 实例对象的属性字典
print(C1.__dict__)
print('1-----------------------------------------')
print(x.__class__)    # <class '__main__.C1'>  输出了对象所属的类
print('2-----------------------------------------')
print(C1.__base__)    # C1类的父类类型的元素
print('3-----------------------------------------')
print(C1.__mro__)     # 类的层次结构
print('4-----------------------------------------')
print(A1.__subclasses__())    # 子类的列表


class Person5(object):

    def __new__(cls, *args, **kwargs):
        print('__new__被调用执行了，cls的id值为{0}'.format(id(cls)))
        obj = super().__new__(cls)           # 2.将实例对象从226步中的cls->228cls
        print('创建的对象的id为：{0}'.format(id(obj)))          # 3.将实例对象从228步中的cls->229 id
        return obj                                           # 4.obj 返回值给232步的self

    def __init__(self, name6, age6):                         # 5.self 将id返回给244步p1
        print('__init__被调用了，self的id值为：{0}'.format(id(self)))
        self.name6 = name6
        self.age6 = age6


print('object这个类对象的id{0]', format(id(object)))
print('Person5这个类对象的id{0]', format(id(Person5)))

# 创建Person类的实例对象


p1 = Person5('枣枣', 6)             # 1.第一步在创建对象__new__(cls)中被调用
print('p1这个person5类的实例对象的id：{0}'.format(id(p1)))
print('------------这部分不是很明白---------')

# 类的浅拷贝和深拷贝
# 1.变量的赋值操作---》只是形成两个变量，实际上还是指向同一个对象
# 2.浅拷贝 ---》Python的拷贝一般都是浅拷贝，拷贝时，对象包含的子对象内容不拷贝，因此，源对象与拷贝对象会引用同一个子对象
# 3.深拷贝---》使用copy模块的deepcopy函数，递归拷贝对象中包含的子对象，源对象与拷贝对象所有的子对象也不相同

# 浅拷贝


class CPU:
    pass


class Disk:
    pass


class Computer:
    def __init__(self, cpu, disk):
        self.cpu = cpu
        self.disk = disk

# (1)变量的赋值


cpu1 = CPU()
cpu2 = cpu1
print(cpu1)
print(cpu2)
# 类的浅拷贝


print('------------------------')
disk = Disk()     # 创建一个硬盘类的对象
computer = Computer(cpu1, disk)     # 创建一个计算机类的对象
# 浅拷贝
import copy
computer2 = copy.copy(computer)
print(computer, computer.cpu, computer.disk)
print(computer2, computer2.cpu, computer2.disk)
print('--------------------------------------------')
# 深拷贝
computer3 = copy.deepcopy(computer)
print(computer, computer.cpu, computer.disk)
print(computer3, computer3.cpu, computer3.disk)



















