# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/29 0:25
"""python解释器使用的是unicode(内存）；.py文件在在磁盘上使用UTF-8储存（外存）；
·内置函数open（）创建文件对象------->语法规则：file=open(filename [,mode, encoding])
被创建的对象 =创建文件对象的函数（要创建或要打开文件的名称[，打开模式默认为只读，默认文本文件中字符的编写格式为gbk"""


file = open('a.txt', 'r')  # 这里的文档里的格式暂时只能用英文，待解决 # 打开文件，并以只读模式打开
print(file.read())  # 读取文件内容
print(file.readlines())
file.close()


# 常用的文件打开模式 按文件中的数据组织形式，文件分为一下两大类
# 文本文件：存储的是普通‘字符‘文本，默认为unicode字符集，可以使用记事本程序打开
# 二进制文件：把数据内容用’字节‘进行存储，无法用记事本打开，必须使用专用的软件打开，例，MP3音频文件，JPG图片.doc文档等
# 打开模式 r 以只读模式打开文件，文件的指针将放在文件开头
# w  以只写模式打开文件，如果文件不存在则创建，如果文件存在，则覆盖原有的内容，文件的指针将放在文件开头
# a 以追加模式模式打开文件，如果文件不存在则创建文件，文件的指针在文件开头，如果文件存在，则在文件末尾追加内容，文件指针在原文件末尾
# b 以二进制方式打开文件，不能单独使用需要与其它模式一起使用，rb,或者wb
# + 以读写的方式打开文件，不能单独使用，需要与其它模式一起使用，a+

file = open('b.txt', 'w')   # 创建文件并写入helloworld
file.write('helloworld')
file.close()

src_file = open('logo.png', 'rb')  # 打开源文件
target_file = open('copylogo.png', 'wb')  # 打开目标文件
target_file.write(src_file.read())   # 读完之后将目标文件写出到...

target_file.close()
src_file.close()
# 对象文件常用的方法
# read([size]) 从文件中读取size个字节或字符内容返回。若省略[size],则读取到文件末尾（读取所有内容）
# readline() 从文本文件中读取一行内容
# readlines(）把文件中每一行都作为独立的字符创对象，并将这些对象放入列表返回
# write(str） 将字符串str内容写入文件
# writelines(s_list) 将字符串列表s_list写入文本文件，不添加换行符
"""seek(offset[,whence] 把文件指针移动到新的位置，offset表示相对于whence的位置：
offset:为正往结束方向移动，为负往开始方向移动
whence不同的值代表不同的含义：0：从文件头开始计算（默认值）1：从文件当前位置开始计算 2.从文件末尾位置开始计算"""
# tell()返回文件指针的当前位置
# flush()把缓冲区的内容写入文件，但不关闭文件
# close（）把缓冲区的内容写入文件，同时关闭文件，释放文件对象的相关资源

file = open('a.txt', 'r')
file.seek(1)
file.flush()
print(file.read())
file.close()
print('with语句（上下文管理器）---》with语句可以自动管理上下文资源，无论什么原因跳出with块，都能确保文件的正确关闭，以此来达到释放资源的目的')
# 格式：with open（'logo.png,'rb'） as scr_file:
#      with 语句体： scr_file.read()
# 说明：open（'logo.png,'rb'）：上下文表达式，结果为上下文管理器---》同时创建一个运行时上下文---》自动调用_enter_()方法，并将返回值赋值给：
# scr_file
# 实现了_enter_()和_exit_()方法--->遵守了上下文管理协议
# 离开运行时上下文，自动调用上下文管理器的特殊方法_exit_()
with open('a.txt', 'r') as file:    # 相当于file = open('a.txt', 'r')
    print(file.read())

"""MyContentMgr实现了特殊方法_enter_()和_exit_()，称为该类对象遵守了上下文管理协议，该类对象的实例对象，称为上下文管理器"""


class MyContentMgr(object):
    def __enter__(self):
        print('enter方法被调用执行了')
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        print('exit方法被调用执行了')

    def show(self):
        print('show方法被调用执行了')


with MyContentMgr() as file:        # 相当于file = MyContentMgr（)
    file.show()

with open('logo.png', 'rb') as src_file:
    with open('copylogo.png', 'wb') as target_file:
        target_file.write(src_file.read())










