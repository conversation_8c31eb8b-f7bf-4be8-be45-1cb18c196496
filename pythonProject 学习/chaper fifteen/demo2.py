# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/29 14:54
print("""目录操作：OS模块是python内置的操作系统功能和文件系统相关的模块，该模块中的语句执行结果通常与操作系统有关，在不同的操作系统运行，
得到的结果可能不一样
os模块与os.path 模块用于对目录或文件进行操作""")

# os模块与操作系统相关的一个模块
import os
# os.system('notepad.exe')
# os.system('calc.exe')
# 直接调用可执行文件
os.startfile('D:\WeChat\WeChat.exe')

# os模块操作目录相关函数
# getcwd() 返回当前的工作目录
# listdir(path)  返回指定路径下的文件和目录信息
# mkdir(path[,mode])  创建目录
# makedirs(path1/path2...[,mode])  创建多级目录
# rmdir(path)  删除目录
# removedirs(path1/path2......) 删除多级目录
# chdir(path） 将path设置为当前工作目录

print(os.getcwd())
lst = os.listdir('../chaper fifteen')
print(lst)

os.mkdir('newdir2')
os.makedirs('A/B/C')
os.rmdir('newdir2')
os.removedirs('A/B/C')

print('os.path模块操作目录相关函数')
# abspath(path） 用于获取文件或目录的绝对路径
# exists（path） 用于判断文件目录是否存在，如果存在返回true,否则返回False
# join(path,name） 将目录与目录或者文件名拼接起来
# splitext()   分离文件名和扩展名
# basename(path)  从一个目录中提取文件名
# dirname（path) 从一个路径中提取文件路径，不包括文件名
# isdir（path)   用于判断是否为路径

import os.path
print(os.path.abspath('demo1.py'))
print('1-----------------------------')
# 列出指定目录下的所有py文件
path = os.getcwd()
lst1 = os.listdir(path)
for filename in lst1:
    if filename.endswith('.py'):
        print(filename)
print('2-----------------------------')
path = os.getcwd()
lst2_files = os.walk(path)
print('3-----------------------------')
for dirpath, dirname, filename in lst2_files:
    print(dirpath)
    print(dirname)
    print(filename)

print('4-----------------------------')

path = os.getcwd()
lst3_files = os.walk(path)
for dirpath, dirname, filename1 in lst3_files:
    for dir in dirname:
        """print(os.path.join(dirpath, dir))"""
    for file in filename1:
        print(os.path.join(dirpath, file))



