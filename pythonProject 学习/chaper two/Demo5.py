# 学习Demo
# 开发时间：2022/11/4 22:25
# coding:UTF-8
name = '张三'
age = 30
print(name, type(name))
print(age, type(age))
# 说明name与age的数据类型不同
# 不同数据类型的数据进行连接需要先进行数据转换
print("我叫"+name+"今年"+str(age)+'岁')    # 连接时用加号连接

print("---------------------str()其它类型转换成str类型-------------")
a = 10
b = 198.8
c = False

print(a, type(a), b, type(b), c, type(c))
print(str(a), str(b), str(c), a+b+c, a, type(str(a)), type(str(b)), type(str(c)))
print("---------------------int()其它类型转换成int类型-------------")
s1 = '128'
f1 = 98.7
s2 = '76.77'
ff = True
s3 = 'hello'
print(s1, type(s1), f1, type(98.7), s2, type(s2), ff, type(ff), s3, type(s3))
print(int(s1), type(int(s1)))         # 将str转换为int时，字符串为数字串
print(int(f1), type(int(f1)))         # 将float转换为int时，取整舍余
# print(int(s2), type(int(s2)))        # ValueError: invalid literal for int() with base 10: '76.77'(字符串为小数串）
print(int(ff), type(int(ff)))
print(int(s3), type(int(s3)))         # 将str转换为int时，必须为数字
