# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/5 22:14
# 多分支结构（if----elif------elif.......else----)
"""A 100-90
   B 89-80
   C 79-70
   D 69-60
   E 60分以下"""
score = float(input('请输入你的分数'))
if score >= 90 and score <= 100:
    print('你的分数是A级')
elif score >= 80 and score <= 89:
    print('你的分数是B级')
elif score >= 70 and score <= 79:
    print('你的分数是C级')
elif score >= 60 and score <= 69:
    print('你的分数是D级')
elif score >= 0 and score <= 59:
    print('你的分数是D级')
else: score > 100 or score < 0, print('你的输入有误')
