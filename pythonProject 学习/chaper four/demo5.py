# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/5 23:00
"""-----------嵌套if--------
    1.判断是否是会员；
    2.是的话，执行会员折扣；
     购买金额大于等于200 打8折；
     购买金额大于等于100 打9折
     其它情况不打折
    3.不是会员
      大于等于200 打9.5折
      其它情况不打折"""
print('''请问你是会员吗？
         输入yes表示是,
         否则表示不是！''')
# 外层判断是否为会员
VIP = input('请输入')
money = float(input('输入你的购买金额'))
# 是会员
if VIP == 'yes':
    if money >= 200:
        print('打8折，你应付的金额是:', money*0.8)
    elif money >= 100and money < 200:
        print('打9折，你应付的金额是：', money*0.9)
    else:money < 100, print('不打折，你应付的金额是：', money)

else:           # 非会员
    if money >= 200:
        print('打9.5折，你应付的金额是:', money * 0.95)
    else: money < 200, print('不打折，你应付的金额是：', money)  # money<200,可以不要，一样的





