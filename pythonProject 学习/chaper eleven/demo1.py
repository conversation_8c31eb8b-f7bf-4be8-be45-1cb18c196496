# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/22 15:33
"""bug常见的类型：粗心导致错误的自查宝典
1，漏了末尾的冒号，如if语句，循环语句，else语句等
2.缩进错误。3.把英文符号写成中文：比如引号，冒号，括号
4.字符串拼接的时候把字符串与数字拼接到一起
5.没有定义变量，比如while循环条件的变量
6.‘==’比较运算符和‘=’赋值运算符的混用"""


lst = [11, 22, 33, 44]        # 列表的缩引从0开始
print(lst)
lst = []
#lst = append('A', 'B', 'C')
lst.append('A')
lst.append('B')
lst.append('C')
print(lst)


lst1 = [{'rating': [9.7, 2062397], 'id':'1292052', 'type': ['犯罪', '剧情'], 'title': '肖生克救赎',
         'actors': ['蒂姆·罗宾斯', '摩根·弗里曼']},
        {'rating': [9.6, 1528760], 'id':'1291546', 'type': ['剧情', '爱情', '同性'], 'title':'霸王别姬',
        'actors':['张国荣', '张丰毅', '巩俐', '葛优']},
        {'rating': [9.5, 1559181], 'id':'1292720', 'type': ['剧情', '爱情'],
        'title':'阿甘正传', 'actors':['汤姆·汉克斯', '罗宾·怀特']}]


name = input('请输入你要查询的演员：')
for item in lst1:              # 遍历列表--得到的是{}，item是一个又一个的字典
    act_lst = item['actors']
    for actor in act_lst:
        if name in actor:
            print(name, '出演了', item['title'])

    """
    for movie in item:          # 遍历字典，得到movie 是一个字典中的key
            print(movie)"""
print("""python---异常处理机制，多个except结构-------按照先子类，后父类的顺序，避免遗漏异常最后增加BaseException
被动掉坑：程序代码没有错，只因为用户的错误操作或者一些例外情况导致程序崩溃""")
try:
    a = int(input('请输入第一个整数'))
    b = int(input('请输入第二个整数'))
    result = a / b
    print('结果为:', result)
except ZeroDivisionError:
    print('对不起，除数不允许为0')
except ValueError:
    print('只能输入数字串')
print('程序结束')
"""try-------except-------else结构；try块中没有抛出异常，则执行else块；try块中抛出异常，则执行except块；"""

try:
    n1 = int(input('请输入第一个整数'))
    n2 = int(input('请输入第二个整数'))
    result = n1 / n2
    print('结果为:', result)
except BaseException as e:
    print('出错了')
    print(e)
else:
    print('结果为:', result)

"""try---except---else---finally结构；finally块无论是否发生异常都会被执行能常用来释放try块中申请的资源；"""

try:
    n1 = int(input('请输入第一个整数'))
    n2 = int(input('请输入第二个整数'))
    result = n1 / n2
    print('结果为:', result)
except BaseException as e:
    print('出错了')
    print(e)
else:
    print('结果为:', result)
finally:
    print('无论是否发生异常都会被执行')
print('程序结束')

print("""常见错误类型：ZeroDivisionError---除0  IndexError---序列索引错误  KeyError---映射中没有这个键 
NameError---未声明/初始化对象 SyntaxError---语法错误 ValueError---传入无效参数""")


