# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/15 13:10
lst = [10, 20, 'python', 'hello']
print(10 in lst)
print('python' not in lst)
for item in lst:                       # 列表遍历
    print(item)
lst1 = [10, 20, 30]
print('添加元素前', id(lst1))
lst1.append(99)                        # 在列表的末尾添加一个元素
print('添加元素后', lst1, id(lst1))      # 添加对象后，ID引用不发生改变
lst1.insert(2, 5)                      # 在任意位置上添加一个位置，前面的1表示列表的索引位置
print(lst1)
lst2 = [True, False, 'hello']
lst1[1:] = lst2                        # 任意位置添加N多个元素，冒号前表示要添加的元素个数
print(lst1)
lst.remove(10)                         # 从列表中移除一个元素，如果有重复元素，只移除第一个元素，如果元素不存在，报错
print(lst)
lst.pop(0)                            # pop()根据索引移除元素,如果索引不存在报错
print(lst)
lst.pop()                              # 如果不指定参数索引，将删除列表中的最后一个元素
print(lst)

print('-----------切片操作-删除至少一个元素，将产生一个新的列表对象------------')
lst3 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
new_list = lst3[1:6]
print('原列表', lst3)
print('新列表', new_list)
print('-------不产生新的列表对象，而是删除原列表中的内容-----如下：')
lst4 = [10, 20, 30, 40, 50, 60, 70, ]
print(id(lst4))
lst4[0:5] = []
print(id([]))
print(lst4)

print('---------列表元素的修改操作：为指定的索引的元素赋予一个新值；为指定的切片赋予一个新值')
lst5 = [22, 11, 6, 65, 88, 95, 55, 45, 38, 88]
print('排序前的列表', lst5, id(lst5))

lst5.sort()                   # 开始排序，调用列表对象的Sort方法，升序排序。默认
print('排序后的升序列表', lst5, id(lst5))
lst5.sort(reverse=True)       # 通过指定关键字参数将列表进行降序排序
print('排序后的降序列表', lst5, id(lst5))

print('-------使用内置函数sorted(）对列表进行排序，将产生一个新的列表对象-----------')
lst6 = [46, 5, 52, 963, 52, 85, 984, 455, 785]
print('原列表', lst6, id(lst6))
new_list = sorted(lst6)
print('新列表', new_list, id(new_list))
print('---------列表生成表达式，[i*i for i in range(10）]-------------')
lst7 = [i*i for i in range(10)]
print("lst7", lst7)
print('''列表中的元素为为偶数''')
lst7 = [i*2 for i in range(10)]
print(lst7)
