# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/14 23:24
# 获取列表中的多个元素------切片操作
lst = [10, 20, 30, 40, 50, 60, 70, 80, ]
#start=1 stop=6 step=1
print(lst[1:6:1])
print('原列表', id(lst))
lst2 = lst[1:6:1]
print('切的片段', id(lst2))      # 原列表片段的拷贝，ID发生改变
print(lst[1:6])                # 默认步长为1
print(lst[:6:1])               # 切片默认第一个元素是列表的第一个元素
print(lst[1::1])               # 切片最后一个元素默认是列表最后一个人元素
print('----------索引为负数的情况-------------')
print(lst[:6:-1])             # 切片第一个元素默认为列表最后一个元素
print(lst[1::-2])             # 切片的最后一个元素默认是列表的第一个元素






