# 学习Demo
# 开发时间：2022/11/2 0:08

# 可以输出字符串
print('helloworld')
print("helloword")
# 含有运算符的表达式
print(3+1)

# 将数据输出到文件中,注意点：1.指定的盘符存在2.使用file=fp
fp = open('D:/text.txt', 'a+')  # a+如果文件不存在就创建；存在就在文件的内容后面继续追加
print("helloword", file=fp)
fp.close()

# 不进行换行输出（输出的内容的内容在同一行中)
print('hello', 'world', 'python')
# 转义字符

print("hello\nworld")     # /  +转义功能首字母  --->newline 首字母表示换行
print('hello\tworld')     # hell占了一个制表位，所以\t占了3个位置
print('helloooo\tworld')
print('hello\rworld')     # world将hello覆盖掉了
print('hello\bworld')     # \b是退一格 将o退没了

print('http:\\\\www.baidu.com')
print('老师说：‘大家好’')
