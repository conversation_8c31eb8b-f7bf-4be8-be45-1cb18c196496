# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/25 23:55
# 什么叫模块 模块英文是Modules
# 函数与模块的关系---》一个模块可以包含N多个函数
# Python中一个扩展名为.py的文件就是一个模块
# 模块使用的好处：
# 1.方便其它程序和脚本的导入并使用 2.避免函数名和变量名冲突 3.提高代码的可维护性 4.提高代码的可复用性
# 自定义模块---》创建模块---》新建一个.py文件，名称尽量不要与Python自带的标准模块相同
# 导入模块  1.import 模块名称  [as 别名]      2.from 模块名称 import 函数/变量/类


import math  # 关于数学运算    导入所有函数内容
print(id(math))
print(type(math))
print(math)
print(math.pi)
print('--------------------------------')
print(dir(math))      # 查看模块内的属性和方法
print(math.pow(2, 3), type(math.pow(2, 3)))
print(math.ceil(9.001))     # 向上取整
print(math.floor(9.9999))   # 向下取整

from math import pi           # 导入指定函数内容
from math import pow
print(pi)
print(pow(2, 3))


def add(a, b):
    return a + b


def div(a, b):
    return a/b

# 如何导入自定义的calc模块 新建章节--->鼠标右键---》Mark Directory as--->Source Root


import calc          # PEP 8: E402 module level import not at top of file
print(calc.add(10, 20))
print(calc.div(10, 4))

from calc import add    # 导入指定函数内容
print(add(34, 56))

print("""以主程序形式运行---》每个模块的定义中都包括一个记录模块名称的变量_name_,程序可以检查该变量，
已确定他们在哪个模块中执行。如果一个模块不是被导入到其它模块中执行，那么它可能在解释器的顶级模块中执行。
顶级模块的_name_变量的值为_main_(详细见calc模块里面是怎么使用的）""")

import calc2      # 这里只是调用calc2的函数，不输出里面的执行结果 30
print(calc2.add(100, 200))
print('---------python中的包----------')
# 包是一个分层次的目录结构，它将一组功能相近的模块组织在一个目录下
# 作用：代码规范   避免模块名冲突
# 包与目录的区别 1.包含_init_.py文件的目录称为包 2.目录里通常不包含_init_.py文件
# 包的导入 import 包名.模块名
# 结构：python程序--->包1.....包n(包中包含模块A,B....)----->模块中包含函数，等一系列判断语句
# 包的导入，如下：
import package1.module_A as ma  # ma是package1.modble_A 这个模块的别名 方便调用模块
import package1.module_B as mb
print(mb.b)
print(ma.a)
# print(package1.module_A.a)        # 这样调用很麻烦
print(ma.a)


# 导入带有包的模块时注意事项
import package1
import calc
# 使用import方式进行导入时，只能跟包名或模块名

from package1 import module_A
from package1.module_A import a
# 使用from...import方式可以导入包，模块，函数，变量

print('---------python中常用的内置模块---------')
# sys---与pyhton解释器及其环境操作相关的标准库
# time---提供与时间相关的各种函数标准库
# os---提供访问操作系统服务功能的标准库
# calendar---提供与日期相关的各种函数的标准库
# urllib---用来读取网上（服务器）的数据标准库
# json---用于使用json序列化和反序列化对象
# re---用于在字符串中执行正则表达式匹配和替换
# math---提供标准算术运算函数的标准库
# decimal---用于进行精度控制运算精度，有效数位和四舍五入操作的十进制运算
# logging---提供了灵活的记录事件，错误，警告和调试信息等日志的功能
# 开头连接了 math模块，这里不再连接
print('-------------------------------------------------')

import sys
import time
import urllib.request   # urllib是一个包，包里含有request 模块

print(sys.getsizeof(24))
print(sys.getsizeof(45))
print(sys.getsizeof(True))
print(sys.getsizeof(False))
print(time.time())
print(time.localtime(time.time()))
# print(urllib.request.urlopen('http://www.baidu.com').read()) # 这里没有搞懂
print(math.pi)
print(dir(urllib))

print('------第三方模块的安装及使用------')
# 第三方模块的安装  pip install 模块名  window+R--->cmd 输入前面实例 安装完成后进行验证---python->进入交互式程序
# 输入import schedule，这种方式称为在线安装
# 待解决 import schedule



