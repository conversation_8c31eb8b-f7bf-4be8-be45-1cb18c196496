# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/16 17:05
"""元组---python,内置的数据结构之一，是一个不可变序列；
不可变序列：元组，字符串；可变序列：列表，字典；"""
lst = [10, 20, 45]
print(id(lst))
lst.append(100)
print(id(lst))
"""不可变序列：元组，字符串"""
s = 'hello'
print(id(s))
s = s+'world'
print(id(s))
print(s)
print('----元组的创建方式-----')
"""第一种创建方式直接使用（）"""
# t = ('python', 'world', 98)
#  print(t)
# print(type(t))
"""元组的第二种创建方式，使用内置函数tuple()"""
t1 = tuple(('python', 'world', 98))
print(type(t1))
print(t1)
t2 = 'python', 'world', 98    # 省略了小括号
print(type(t2))
print('t2=', t2)
t3 = ('python',)              # 只包含了一个元组的元素，需要使用逗号和小括号
print('---空元组，空列表，空字典------')
t4 = tuple()
t5 = ()
lst = []
lst1 = list()
d1 = {}
d2 = dict()
print("""将元组设计成不可变序列的原因---多任务环境下，同时操作对象时不需要加锁
因此，程序中尽量使用不可变序列-------------------------------------
注意事项：元组中储存的是对象的引用a)元组中对象本身是不了变对象，则不能在引用其它对象
b)如果元组中的对象是可变对象，则可变对象的引用不允许改变，但数据可以改变""")
t = (10, [20, 30], 9)
print(t)
print(type(t))
print(t[0], type(t[0]), id(t[0]), t[1], type(t[1]), id([t1]), t[2], type(t[2]), id(t[2]))
"""尝试将t[1]修改成100"""
# t[1] = 100                    # 元组是不允许修改元素的
t[1].append(100)               # t[1]列表是可变对象，则可变对象的引用不允许改变，但数据可以改变
print(t, id(t[1]))
"""元组的遍历"""
t6 = ('python', 'world', 98)
t7 = tuple(('python', 'world', 98))       # 元组的创建-两种方式
"""第一种获取元组的方式---使用索引"""
print(t6[0])
print(t6[1])
print(t6[2])
# print(t6[3])                               # IndexError: tuple index out of range

"""第二种获取元组的方式---for---in"""
for item in t7:
    print('item=', item)




















