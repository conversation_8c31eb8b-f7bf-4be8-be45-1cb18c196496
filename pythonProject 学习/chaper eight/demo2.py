# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/16 23:07
"""什么是集合，Python语言提供的内置数据结构，与列表字典一样都属于可变类型的序列；
集合是没有value的字典-----集合的创建方式：直接{}---或者使用内置函数set（）"""
s = {1, 2, 3, 4, 5, 6, 7, 5, }        # 集合中的元素不允许重复
print('s=', s)
s2 = set(range(1, 11))
print('s2=', s2, type(s2))
s3 = set([1, 2, 3, 5, 6, 65])       # 集合中的元素时无序的
print('s3=', s3, type(s3))
s4 = set('python')
print('s4=', s4, type(s4))
s5 = set({11, 21, 13, 14, 16})
print('s5=', s5, type(s5))
print('-----定义一个空集合------')
s6 = {}
print('s6=', s6, type(s6))      # <class 'dict'>
s7 = set()
print('s7=', s7, type(s7))
print('----集合元素的判断操作----in---/not---in')
s = {10, 20, 30, 405, 60}
print(10 in s)
print(65 not in s)
"""集合元素的添加操作"""
s.add(100)                   # add（）方法 一次添加一个元素
s.update({200, 500, 400})    # update()方法 至少添加一个元素
print(s)
"""集合元素的删除操作"""
s.remove(100)                # 一次删除一个指定元素，如果指定元素不存在抛出keyError
s.discard(500)               # 一次删除一个指定元素，如果指定元素不存在不抛出异常
print('-------------------------------------------')
# s. pop(400)                 # TypeError: set.pop() takes no arguments (1 given)
s.pop()                       # 一次删除任意一个元素
print(s)
s.pop()
print(s)
s.clear()                      # 清空集合
print(s)
print('------集合间的关系--------')
# 1.判断两个集合是否相等
s = {10, 20, 30, 40}
s1 = {20, 30, 10, 40}
print(s == s1)
print(s != s1)
print("""一个集合是否是另一个集合的子集""")
s2 = {10, 20, 30, 40, 50, 60}
s3 = {10, 20, 30, 40}
s4 = {10, 20, 90}
print(s3.issubset(s2))          # s3是s2的子集 True
print(s4.issubset(s2))          # s4是s2的子集 false
print("""一个集合是否是另一个集合的超集""")
print(s2.issuperset(s3))
print(s2.issuperset(s4))
print('-----------两个集合是否含有交集----------------')
print(s2.isdisjoint(s5))         # 无交集为真
s5 = {452, 496, 46, }
print(s2.isdisjoint(s4))         # 有交集为假
print('--------------集合的数学操作--------------------')
print('# 1.交集')
s6 = {10, 20, 30, 40}
s7 = {20, 30, 40, 50, 60}
print(s6.intersection(s7))       # intersection（）与&，等价；交集操作
print(s6 & s7)
print(s6)
print(s7)
print('# 2.并集操作')
print(s6.union(s7))
print(s6 | s7)                    # union 与 | ，等价，并集操作
print(s6)
print(s7)
print('# 3.差集操作')
print(s6.difference(s7))
print(s6-s7)
print(s6)
print(s7)
print('4.对称差集')
print(s6.symmetric_difference(s7))
print(s6 ^ s7)
print(s6)
print(s7)
print("""--------------集合生成式，集合生成的公式：{i*i for i in range(1,10)};
将{}改成[],就是列表生成式;没有元祖生成式；--------------------""")































