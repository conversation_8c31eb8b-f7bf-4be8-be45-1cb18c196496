# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/21 10:12
"""函数的创建和调用--->什么是函数:函数就是执行特定的任务和以完成特定功能的一段代码；
为什么需要函数1.复用代码   2.隐藏实现细节    3.提高可维护性    4.提高可读性以便于调试
函数的创建：  def 函数名（[输入参数]):
                函数体
                [return  xxx]"""


def calc(a, b):
    c = a+b
    return c


result = calc(10, 20)
print(result)
"""函数调用的参数传递 1.位置实参--->根据形参对应的位置进行实参传递
                   2.关键字实参--->根据形参名称进行实参传递"""


def fun(arg1, arg2):
    print('arg1=', arg1)
    print('arg2=', arg2)
    arg1 = 100
    arg2.append(10)
    print('arg1=', arg1)
    print('arg2=', arg2)
    return


n1 = 11
n2 = [22, 33, 44]
fun(n1, n2)       # 将位置传参，arg1,arg2,是函数定义时的形参；n1,n2是函数调用时的实参，总结，实参名称和形参名称可以不一致
print('n1=', n1)
print('n2=', n2)


"""在函数的调用过程中，进行参数传递
如果是不可变对象，在函数体的修改不会影响实参的值 arg1的修改为100，不会影响n1的值
如果是可变对象，在函数体的修改会影响实参的值 arg2的修改为，append（10）会影响n2的值"""


print('------函数的返回值--->函数返回多个值时，结果为元组------')


def fun1(num):
    odd = []         # 存奇数
    even = []        # 存偶数
    for i in num:
        if i % 2:              # i与2的余数的布尔值 为ture （0的bool值为 false；非0的bool值为 ture）
            odd.append(i)      # 判断为奇数
        else:
            even.append(i)
    return odd, even           # 注意return的位置

print(bool(0))
# 函数的调用
lst = [10, 29, 34, 23, 44, 53, 55]
print(fun1(lst))


"""函数的返回值
 （1）如果函数没有返回值【函数执行完成完毕之后，不需要给调用处提供数据】 return可以省略不写
 （2）函数的返回值，如果是1个，直接返回类型
 （3)函数的返回值，如果是多个，返回的结果为元组"""


def fun2():        # 这个函数没有返回值，不需要return直（省略）
    print('hello')


fun2()


def fun3():
    return'hello'


res = fun3()
print(res)


def fun4():
    return'hello', 'world'


print(fun4())
"""函数在定义时，是否需要返回值视情况而定"""


def fun5(a, b=10):       # b 称为函数默认参数
    print(a, b)


# 函数的调用
fun5(100)
fun5(20, 30)             # 30代替了默认的参数10，相当于你有替换值的时候替换，没有的时候就用默认值

print('hello', end='\t')    # print圆满定义处，默认换行输出，指定\t 进行不换行输出
print('world')

print('----------------函数的参数定义-----------------')
"""个数可变的位置参数--->使用*定义个数可变的位置形参---》结果为一个元组
    个数可变的关键字形参--->使用**定义个数可变的关键字形参---》结果为一个字典"""


def fun6(*args):         # 函数定义时的 可变的位置参数
    print(args)          # 结果为一个元组
    print(args[0])


fun6(10)
fun6(10, 30)
fun6(30, 405, 50)


def fun7(**args):
    print(args)


fun7(a=10)
fun7(a=20, b=30, c=40)

        # print圆满定义处,是一个位置可变的位置形参*args，所以可以传几个都行


"""def fun8(*args, *a)
    pass
    以上代码会报错个数可变的位置参数只能是1个
def fun9(**args, **args)
    pass
    以上代码会报错个数可变的关键字参数只能是一个"""


def fun10(*args1, **args2):
    pass


"""def fun11(**args, *args2):
    pass
    在一个函数定义过程中，既有个数可变的关键字形参，也有个数可变的位置形参，
    要求个数可变位置形参放在个数可变的关键字形参之前"""

print('-----------函数的调用总结-----------')


def fun12(a, b, c):       # a,b,c在函数定义处，所以是形式参数
    print('a=', a)
    print('b=', b)
    print('c=', c)


# 函数的调用
fun12(10, 20, 30,)     # 函数调用时的参数传递，称为位置传参
lst = [11, 22, 33]
fun12(*lst)            # 函数在调用时，将列表中的每个元素都转化为位置实参传入
print('------------------------------------------------------')
fun12(a=100, b=200, c=300)    # 函数的调用，所以是关键字实参
dic = {'a': 111, 'b': 222, 'c': 333}
fun12(**dic)           # 函数调用时 ，将字典中的键值对，都转化为关键字实参传入


def fun12(a, b=10):     # b在函数定义处，所以b是形参，而且进行了赋值，所以b称为默认形参
    print('a=', a)
    print('b=', b)


def fun13(*args):       # 个数可变的位置形参
    print(args)


def fun14(**args3):     # 个数可变的关键字形参
    print(args3)


fun13(10, 20, 30, 40)
fun14(a=11, b=22, c=33, d=44, e=55)


def fun15(a, b, c,d):
    print('a=', a)
    print('b=', b)
    print('c=', c)
    print('d=', d)


# 调用fun15函数
fun15(10, 20, 30, 40)           # 位置实参传递
fun15(a=10, b=20, c=30, d=40)   # 关键字实参传递
fun15(10, 20, c=30, d=40)       # 前两个采用位置实参传递 c,d采用关键字实参传递
"""需求，c,d只能采取关键字实参传递"""


"""函数定义时的形参顺序问题"""


def fun16(a, b, *, c, d, **args):
    pass


def fun17(*args3, **args5):
    pass


def fun19(a, b=10, *args6, **args7):
    pass


print('--------------变量的作用域------------------')
"""变量的作用域：1.程序代码可以访问的区域
            2.根据程序的有效范围可分为1）局部变量，函数内定义并使用的变量，只有在函数内部有效，
            局部变量使用global声明，这个变量就会变成全局变量
            2）  全局变量---》函数体外定义的变量，可用于函数内外"""


def fun20(a, b):         # c就称为局部变量，因为c是在函数体内定义的变量，a,b为函数的形参，作用范围也是函数的内部，相当于局部变量
    c = a+b,
    print(c)

# print(c)    a,c作用域外报错
# print(a)


name = '杨老师'
print(name)


def fun21():
    print(name)
# 函数的调用


fun21()


age = 0            # 必须先定义变量，否则出错


def fun22():
    global age     # 函数内部定义的变量，局部变量，使用global 声明后就变成全局变量
    age = 20
    print(age)


fun22()
print(age)


print('--------------递归函数--------------')

"""如果一个函数的函数体调用了该函数本身，这个函数就称为递归函数；
缺点：占用内存多，效率低下；优点思路和代码简单"""


def fac(n):      # factorial 阶乘
    if n == 1:
        return 1
    else:
        return n*fac(n-1)


print(fac(5))    # 用单调试，查看状态 将实参6，5,4,3,2依次带入；


def fac(n):      # factorial 阶乘
    if n == 1:
        return 1
    else:
        wer = n*fac(n-1)
        return wer


print(fac(6))
print('-------------------斐波那契数列------------------------')
"""一个数列从第三项开始，每一个数都是前两个数的和；也叫兔子数列"""


def fib(n):
    if n == 1:
        return 1
    elif n == 2:
        return 1
    else:
        return fib(n-1) + fib(n-2)


# 斐波那契数列上的第6位上的数字
print(fib(6))

print('-------------------------------------------------')
# 输出这个数列上的前6位上的数字
for i in range(1, 7):
    print(fib(i))









