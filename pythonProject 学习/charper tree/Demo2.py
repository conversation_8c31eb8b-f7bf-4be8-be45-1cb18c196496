# 学习Demo
# 开发时间：2022/11/5 11:50
print("python中的算术运算符")
print(1+2)
print(9-7)
print(3*6)
print(9/3)
print(9.9//5)  # 取整    一正一负向下取整
print(2**2)    # 2的2次方
print(2**3)    # 2的3次方
print(9 % -4)    # 取余数(一正一负按公式：余数=被除数-除数*商）例如：9/-4  余数=9-（-4）*（-3） 余数为-3

print("python中的赋值运算符")

a = b = c = 20       # 链式赋值          注意系统函数写前面  ID（） type()
print(a, id(a))
print(b, id(b))
print(c, id(c))

print("----------以下为解包赋值-----------")
a, b, c = 20, 30, 40         # 每个参数必须一一对应
print("-------------以下为参数赋值---------------")
a += a        # 相当于a+a=a
print(a)

a += 30       # 相当于a+30=a
print(a)

a -= 10       # 相当于a-10=a
print(a)

a *= 2          # 相当于a*2=a
print(a)
print(a, type(a))

b //= 2
print(b)
print(b, type(b))

c %= 3
print(c)
print(c, type(c))

print("________比较运算符________")
a, b = 10, 20
print('a>b吗？', a > b)
print('a<b吗？', a < b)
print('a==b吗？', a == b)
print('a!=b吗？', a != b)
print('a>=b吗', a >= b)
print('a<=b吗', a <= b)
'''一个=表示赋值运算，两个==表示比较运算符
一个变量由三个部分组成：标识id,类型(type),值Value
==比较的是值还是id？------值
比较id用-------is------'''
a = 10
b = 10
print(a == b)      # True,表示a和b的值相等
print(a is b)      # True,表示a与b的标识id相等
print(a, id(a))
print(b, id(b))
print('------------------------------------------')
lst1 = [22, 33, 44, 55]
lst2 = [22, 33, 44, 55]
print(lst1 == lst2)
print(lst1 is lst2)
print(a is not b)
print(lst1, id(lst1))
print(lst2, id(lst2))

print('--------布尔值之间的运算符-------')
a, b = 2, 3
print(a == 2 and b == 3)
print(a == 5 or b == 3)
print('-------not 取反-----------')
f1 = True
f2 = False
print(not f1)
print(not f2)
print('--------in----------not in-----------')
s = "helloworld"
print('w' in s)
print('o'not in s)
print('-------------------位运算符-------------&------|---------->>-------<<--------------')
print(4 & 8)      # 按位与&同为1时为1
print(4 | 8)      # 按位或|，同为0时，结果为0
print(4 >> 1)     # 向右移动一位，相当于除以2
print(4 << 2)     # 向左一定2位，相当于除以4
