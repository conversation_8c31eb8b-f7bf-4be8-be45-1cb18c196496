# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/22 22:04
"""编程界的两大思想1)面向过程   事物比较简单-可以用线性的思维去解决
                2）面向对象    事物比较复杂-使用简单的线性思维难以解决
                 共同点：都是解决实际问题的一种思维方式"""
"""类与对象---数据类型1.不同的数据类型属于不同的类
                   2.使用内置函数查看数据类型
            对象：1,100，99，520，都是int类之下包含的相似的不同个例，这个个例的专业术语称为实例或对象"""

"""python中一切皆对象---------------类的创建"""


class Student:    # Student为类名称（类名）由一个或者多个单词组成，每个单词首字母大写，其余小写
    native_place = '吉林'          # 类属性 直接写在类里的属性称为类属性    类中方法外的变量称为类属性，被该类所有的对象共享

    def __init__(self, name, age):  # name age 为实例属性（def在类里面定义称为方法；在类外面定义称为函数)  init初始化方法
        self.name = name
        self.age = age
    # 实例方法

    def info(self):
        print('我的名字叫', self.name, '今年', self.age)
    # 类方法     采用@classmethod修饰的方法  使用类名直接访问的方法  Student.cm 调用类方法

    @classmethod
    def cm(cls):
        print('类方法')

    # 静态方法  使用staticmethod进行修饰   使用类名直接访问的方法   Student.sm 调用静态方法
    @staticmethod
    def sm():
        print('静态方法')

# 类的组成-----》类属性，实例方法，静态方法，类方法


""""------对象的创建---------又称类的实例化  语法：实例名=类名（)"""

# 创建Student类的实例对象
stu = Student('Jack', 20)
print(stu.name)            # 实例属性
print(stu.age)             # 实例属性
stu.info()                 # 实例方法的调用
# 类属性的使用方式
# print(Student.native_place) 访问类属性
stu1 = Student('张三', 20)
stu2 = Student('李四', 30)
print(stu1.native_place)
print(stu2.native_place)
Student.native_place = '天津'
print(stu1.native_place)
print(stu2.native_place)
Student.cm()               # 调用类方法
print('---------动态绑定属性和方法--------python是动态语言，在创建对象后之后，可以动态地绑定属性和方法------')


def show():
    print('我是一个函数')


stu = Student('Jack', 20)
stu.gender = '男'         # 动态绑定性别
print(stu.name, stu.age, stu.gender)
stu.show = show           # 动态绑定方法
stu.show()                # 调用show方法

# 一个Student类可以创建N多个Student类的实例对象，每个实体对象的属性值不同




