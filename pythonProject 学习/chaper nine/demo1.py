# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/19 9:48
"""字符串的驻留机制的优缺点:避免频繁创建和销毁，
提升效率和节约内存；字符串拼接时建议使用join()方法---效率高"""
a = 'python'
b = "python"
c = '''python'''
print(a, id(a))
print(b, id(b))
print(c, id(c))
s = 'hello, hello'
print(s.index('lo'))        # 3  查找子串第一次出现的位置
print(s.find('lo'))         # 3  查找子串第一次出现的位置 不存在返回-1
print(s.rindex('lo'))       # 10  查找子串最后一次出现的位置
print(s.rfind('lo'))        # 10  查找子串最后一次出现的位置 不存在返回-1
# print(s.index('k'))       # ValueError: substring not found
print(s.find('k'))          # -1
# print(s.r index('k'))        # ValueError: substring not found
print(s.rfind('k'))         # -1
print('---------字符串大小写转换操作----------')
s1 = "hello,python"
a = s1.upper()              # 转换成大写后，会产生一个新的字符串对象
print(a, id(a))
print(s1, id(s1))
b = s1.lower()              # 转换成小写，会产生一个新的字符串对象
print(b, id(b))
print(s1, id(s1))
print(b == s1)
print(b is s1)
print('-----------------居中对齐---------------')
s3 = 'hello,python'
print(s3.center(20, '*'))
print('''左对齐''')
print(s3.ljust(20, "*"))
print(s3.ljust(10, "*"))
print('''右对齐''')
print(s3.rjust(20, "*"))
print(s3.rjust(10, "*"))
print('右对齐，用0进行填充')
print(s3.zfill(20))
print(s3.zfill(10))
print('-8910'.zfill(8))
print('----------字符串的分割---------')
s4 = 'hello world python'
lst = s4.split()
print(lst)
s4 = 'hello|world|python'
print(s4.split(sep='|'))
print(s4.split(sep='|', maxsplit=1))
'''---------rsplit从右侧开始劈分--------'''
print(s4.rsplit(sep='|'))
print(s4.rsplit(sep='|', maxsplit=1))
print('-----------判断字符串操作的方法----------')
s5 = 'hello, python'
print('1.', s.isidentifier())                 # 判断指定字符串是不是合法标识符
print('2.', 'hello'.isidentifier())
print('3.', '张三_'.isidentifier())
print('4.', '张三_123'.isidentifier())

print('5', '\t'.isspace())                    # 判断指定的字符串是否全部由空白组成（回车，换行，水平制表符)

print('6', 'abc'.isalpha())                   # 判断字符串是否全部由字母组成
print('7', '张三'.isalpha())
print('8', '张三1'.isalpha())

print('9', '123'.isdecimal())                 # 判断字符串是否全部由十进制组成
print('10', '123四'.isdecimal())
print('11', 'ⅡⅡⅡ'.isdecimal())

print('12', '123'.isnumeric())                 # 判断字符串是否全部由数字组成
print('13', '123四'.isnumeric())
print('14', 'ⅡⅡⅡ'.isnumeric())

print('15', 'abc1'.isalnum())                  # 判断字符串是否全部由数字和字母组成
print('-------------字符串的替换和合并方法replace()/join()------------------')
s6 = 'hello,python'
print(s6.replace('python', 'java'))
"""第一个参数指定被替换的子串，第二个参数指定替换子串的字符串，
 该方法返回替换后得到的字符串；替换前的字符串不发生改变
调用该方法时，可以通过第三个参数指定最大替换字数"""
s7 = 'hello,python,python,python'
print(s7.replace('python', 'java', 2))

lst = ['hello', 'java', 'python']
print('|'.join(lst))                             # 将列表或者元组中的字符串合并成一个字符串
print(''.join(lst))

t = ('hello', 'java', 'python')
print('*'.join('python'))
print('--------------字符串的比较操作--------比较的是ordinal value’原始值')
print('---ord()得到的是原始值；str（）得到的是与原始值对应的字符')
print('apple' > 'app')
print('apple' > 'banana')
print(ord('a'), ord('b'))
print(ord('杨'))                                  # 输出 杨 的原始值

print(chr(97), chr(98))
print(chr(26472))                                 # 原始值对应的字符
"""== 与 is的区别：前者比较的是值value,后者比较的是标识符id"""

"""字符串的切片操作，字符串是不可变类型，切片操作将产生新的对象"""
s8 = 'hello,python'
s9 = s8[:5]                                       # 由于没有指定起始，从0开始切
s10 = s8[6:]                                      # 没有指定结束，切到最后一位一个元素
s11 = '!'
newstr = s9 + s11 + s10
print(s9)
print(s10)
print(newstr)

print('--------切片[start：stop：step]---------')
s12 = 'hello,world,python'
print(s12[1:5:1])   # 从1开始切到5（不包含5),步长为1
print(s12[::2])     # 默认从0开始，默认到最后一个元素，步长为2
print(s12[::-1])    # 从最后一个元素开始，到字符串第一个元素结束；因为步长为负数
print(s12[-6::])    # 从索引为-6开始，到字符串的最后一个元素结束，步长为1

print('---为什么需要格式化字符串:字符串的拼接操作会开辟很多的新的内存空间---')
# (1） % 作为占位符  %S---> 字符串; %i或%d---> 整数；f%--->浮点数
name = '张三'
age = 20
print('我叫%s,今年%d岁了' % (name, age))  # 第三个百分号是 固定符号%；连接实际值
# （2）{} 作占位符

print('我叫{0}，今年{1}岁了'.format(name, age))  # format()方法--->格式化
# （3) f-string f代表格式化字符串

print(f'我叫{name}，今年{age}岁了')

print('----------------------------------------------------------------')
print('%10d' % 99)                 # 10表示的是宽度
print('%.3f' % 3.1415926)          # .3表示小数点后三位
# 同时表示宽度和精度
print('%10.3f % 3.1415926')        # 一共总宽度为10，小数点后三位
print('----用花括号{}占位符来实现，宽度和精度的表示-----')
print('{0}'.format(3.1415926))     # 0,表示索引(可以省略不写)
print('{0:.3}'.format(3.1415926))  # .3表示一共三位数
print('{:.3f}'.format(3.1415926))  # .3f表示小数点后三位
print('{:10.3f}'.format(3.1415926))   # 同时设置精度和宽度，一共是10位，三位小数

print('------------为什么需要字符串的编码转换，两台计算机的数据传输是以byte字节传输的------------')
s13 = '天涯共此时'
# 编码
print(s13.encode(encoding='GBK'))          # 在GBK这种编码格式中 一个中文占两个字节
print(s13.encode(encoding='UTF-8'))        # 在uft-8这种编码格式中 一个中文占三个字节
# 解码
# byte代表的就是一个二进制数据(字节类型的数据）
byte = s13.encode(encoding='GBK')          # 编码
print(byte.decode(encoding='GBK'))         # 解码

byte = s13.encode(encoding='UTF-8')          # 用什么格式编码就用什么格式解码、常用在爬虫中
print(byte.decode(encoding='UTF-8'))



















