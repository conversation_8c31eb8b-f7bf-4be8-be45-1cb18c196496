#  优先级高的资源覆盖优先级低的资源

# class D(object):
#     age = "d"
#     pass
#
#
# class C(D):
#     age = "c"
#
#     def test(self):
#         print("test")
#     pass
#
#
# class B(D):
#     # age = "b"           # 变量覆盖或属性覆盖
#     def test(self):        # 方法覆盖  指优先级高的类的方法覆盖优先级低的类的方法
#         print("test_b")
#         print(self)
#
#     @classmethod               # 1.这里的self和cls到底指谁？
#     def test2(cls):            # 2.谁调用就是谁（就传谁）
#         print("test2")
#         print(cls)
#
#
#
# class A(B, C):
#     pass


# print(A.mro())  # [<class '__main__.A'>, <class '__main__.B'>, <class '__main__.C'>, <class '__main__.D'>, <class 'object'>]
# print(A.age)  # d
# A().test()  # test
# A().test2()
# A.test2()  # test2 <class '__main__.A'>

print("我是分割线-------------------------资源累加---------------------------")
# 概念：在一个类的基础之上，增加一些额外的资源；
# 子类相比于父类，多一些自己特有的资源；
# 在被覆盖的方法基础之上，新增内容；

# class B:
#     a = 1
#     def __init__(self):
#         self.b = 2                   # 注意这里是新增属性谁调用就是给谁增加属性
#
#     def t1(self):
#         print("t1")
#
#     @classmethod
#     def t2(self):
#         print("t2")
#
#     @staticmethod
#     def t3():
#         print("t3")
#
# class A(B):
#     c = 3
#
#     def __init__(self):          # 自己本身有初始化方法，后不再继承父类的初始化方法，（自己的init覆盖父类的init）这里执行报错了
#         self.e = 666             # AttributeError: 'A' object has no attribute 'b'
#         B.__init__(self)         # 通过类名调用父类的初始化方法 备注掉：可以查看错误
#                                  # 但是这里不能解决类名称变更问题，所以还是建议使用super()方法（万一有多个父类呢，那要改的地方就多了，不利于代码维护）
#                                  # 这里的self指的是A的实例对象，而不是B的实例对象，所以不能调用B的实例方法
#     def tt1(self):               # 给自己额外的增加属性和方法
#         print("tt1")             # 这里的self指的是A的实例对象，而不是B的实例对象，所以不能调用B的实例方法
#
#     @classmethod                 # super（）方法；
#     def tt2(self):
#         print("tt2")
#
#     @staticmethod
#     def tt3():
#         print("tt3")
#     pass
#
#
# a_obj = A()
# print(A.a)
# print(a_obj.b)
#
# a_obj.t1()      # 实例方法通过实例调用
# A.t1(a_obj)     # 类方法通过类调用
# A.t2()
# A.t3()
#
# print(A.c)
#
# a_obj.tt1()      # 实例方法通过实例调用
# A.tt1(a_obj)     # 类方法通过类调用
# A.tt2()
# A.tt3()
#
# a_obj.d = "新增属性"
# print(a_obj.d)
#
# print(a_obj.e)


# class D(object):
#     def __init__(self):
#         print("d")
#
# class B(D):
#     def __init__(self):
#         D.__init__(self)
#         print("b")
#
# class C(D):
#     def __init__(self):
#         D.__init__(self)
#         print("c")
#
# class A(B, C):
#     def __init__(self):
#         B.__init__(self)
#         C.__init__(self)          # A() 这里会将d 重复打印出来 d b d c a  通过类名调用父类产生的问题
#         print("a")
#
#
# # B()
# # C()
# A()


print("我是分割线-------------------------super()方法-------------------------------")
# 概念：super()方法可以调用父类的方法，并且可以解决类名称变更问题______其实就是一个类
# 只在新式类中才有super()方法，经典类没有super()方法；
# 起着代理的作用，帮助我们完成以下任务：
#                               1.沿着MRO链条，找到下一节点，去调用对应的方法；————由于支持多继承，这里的super并不是严格意义上的父类，而是MRO链条上的下一个节点；
#                               1.1沿着谁的MRO节点------------>参数2
#                               1.2找谁的下一节点------------>参数1
#                               1.3如何对应类方法、静态方法、实例方法的传参问题：------------>使用参数2进行调用；

#                               2.语法原理：
#                                        super(参数1,[参数2])
#                                        def super(cls,inst):
#                                            mro = inst.__class__.mro()
#                                            return mro[mro.index(cls)+1]
#                               3.常用语法格式：
#                                   python2.2+:
#                                   super(type,obj)->bound super object;
#                                   super(type, type2)->bound super object;
#                                   python3+:
#                                   super()
#

class B:
    a = 1
    def __init__(self):
        self.b = 2                   # 注意这里self指的是A的实例对象，相当于通过173行的super(A, self).__init__()调用了B的实例方法，给A的实例对象增加属性b和xxx;
        self.xxx = "123"

    def t1(self):
        print("t1")

    @classmethod
    def t2(cls):
        print(cls)                 # 类方法通过类调用，这里的cls指的是类A，而不是实例对象A，所以不能调用实例方法，只能调用类方法；
        print("t2")

    @staticmethod
    def t3():
        print("t3")

class A(B):
    c = 3

    def __init__(self):
        self.e = 666
        super(A, self).__init__()  # 找谁的下一节点------------>参数1------------>A
                                   # 沿着谁的MRO节点（A的子类或者本身）------------>参数2------------>self

        # super().__init__()         # 等价于super(A, self).__init__() # python3+


    def tt1(self):
        print("tt1")

    @classmethod
    def tt2(cls):
        super(A, A).t2()   # 类方法通过类调用，这里的A指的是类A，而不是实例对象A，所以不能调用实例方法，只能调用类方法；
        print("tt2")

    @staticmethod
    def tt3():
        print("tt3")
    pass

a = A()
print(a.__dict__)
A.tt2()



#计算圆的面积
import math
class Circle:
    def __init__(self, r):
        self.r = r
        def area(self):
            return math.pi * self.r ** 2

c = Circle(3)
print(())
















