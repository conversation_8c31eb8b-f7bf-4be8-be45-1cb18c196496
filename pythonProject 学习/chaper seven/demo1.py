# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/15 16:27
"""字典的创建，最常用的方式：使用花括号{}，例如：scores={'张三'：98，'李四'：96，'王五'：65}"""
scores = {'张三': 98, '李四': 96, '王五': 65}
print(scores)
print(type(scores))
"""字典的第二种创建方式：使用内置函数dict()"""
student = dict(name='jack', age=23)
print(student)
"""空字典"""
d = {}
print(d)
f = dict()
print(f)
"""获取字典的元素的值"""
scores = {'张三': 98, '李四': 95, '王五': 65}
print(scores['张三'])                  # 使用[]获取
# print(scores['陈六'])                # KeyError: '陈六'
print(scores.get('张三'))              # 使用get（）方法获取
print(scores.get('陈六'))              # None
print(scores.get('麻七', 99))          # 99是在查找麻七的所对的Value不存在时，提供的一个默认值
# print(scores['麻七'])
"""key的判断"""
scores = {'张三': 98, '李四': 95, '王五': 65}
print('张三' in scores)
print('张三' not in scores)
"""用del删除指定的键值对"""
del scores['张三']
print(scores)
"""清空字典元素clear"""
scores.clear()
print(scores)
"""新增元素"""
scores['陈六'] = 100
scores['王二'] = 50
print(scores)
print('---------获取字典的三种方法---------------')
# 获取所有的keys()
scores1 = {'赵二': 99, '张三': 95, '李四': 88, '王五': 76}
keys = scores1.keys()
print(keys)
# 获取所有的Values()
values = scores1.values()
print(values)
# 获取所有的键值对items()
items = scores1.items()
print(items)
print(list(items))         # 元组（）
print(type(items))
print('-----------字典元素的遍历(遍历的是键值),[]和get获取的是值---------------')
for item in scores1:
    print(item, scores1[item], scores1.get(item))

"""字典的特点，1.所有元素都是一个key-value对，key不允许重复，value可以
             2.字典中的元素是无序的
             3.字典中的key必须是不可变对象
             4.字典也可以根据需要进行动态伸缩
             5.字典会浪费较大的内存，是一种使用空间换时间的数据结构"""
d = {'name': '张三', 'name': '李四'}
print(d)
d = {'name': '张三', 'nikename': '张三'}
print(d)
lst = [10, 20, 30]
lst.insert(1, 100)
print(lst)
# d = {lst: 100}      # 字典中的key必须是不可变对象
print(d)
print("""-----内置函数zip()-----用于将可迭代的对象作为参数，
将对象中对应的元素打包成元组，然后返回由这些对象组成的元组""")
items = ['fruits', 'books', 'others']
prices = [96, 78, 85]
lst1 = zip(items, prices)
print(list(lst1))
print("""-----字典生成式------item.upper()键的表达式 ：price 值得表达式；
 item, price 自定义表示键-值的变量     """)
d = {item.upper(): price for item, price in zip(items, prices)}
print(d)













