# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/8 21:15
# 从键盘录入密码，最多录三次，如果正确就结束循环；break，用于结束循环结构：
# 通常与分支结构if一起使用

# or item in range(3):            # 这里注意位置对齐item
    # paw = input('请输入你的密码')
    # if paw == '6666':
         # print("密码正确")
        # break
    # else:
        # print('密码错误')

"""老是容易把格式整错"""
a = 0                          # 初始化变量
while a < 3:                   # 条件判断
    paw = input('请输入你的密码')
    if paw == '6666':
        print("密码正确")
        break
    else:
        print('密码错误')
# 上面部份全是循环体
    a += 1                      # 改变变量
