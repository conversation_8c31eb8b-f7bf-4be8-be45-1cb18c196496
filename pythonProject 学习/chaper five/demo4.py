# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/7 22:47
# for----in 循环的理解:第一次循环取出字母p......将p的值赋给item,然后将item的值输出
for item in 'python':
    print(item)

print('用for---in 循环计算1-100的偶数和')

num = 0         # '''偶数和'''
item = 0        # 转存变量

for item in range(1, 101):    # 注意书写格式；range产生一个整数序列，是一个可迭代的对象
     if item % 2 == 0:           '''迭代的理解：迭代器---迭代取值的工具;迭代即更新换代，每次的更新都必须依赖上一次的结果；
       num+=item                 可迭代的对象内置有__iter__方式的都可以称之为可迭代对象----内置的意思是可以通过点的方式直接查看到的；'''

print(num)
