# 学习Demo
# 创建人：ZY
# 开发时间：2022/11/6 17:54
# range（）的三种创建方式
"""第一种创建方式，括号里只有一个参数，表示数据的个数"""

r = range(10)   # [0,1,2,3,4,5,6,7,8,9,]默认从零开始，默认相差一个步长
print(r)        # range(0, 10)
print(list(r))  # 用于查看range对象中的整数序列--》list 表示列表
'''第二种创建方式，括号里给了两个参数，起始值和结束值'''
r = range(1, 10)  # 指定了起始值，从1开始，到10结束(不包含10），默认步长为1
print(list(r))
'''第三种创建方式，括号里给了三个参数，起始值和结束值与步进值'''
r = range(1, 10, 2)
print(r)
print(list(r))
# 判断指定的整数在序列中是否存在？ in  、not in，
print(10 in r)
print(9 in r)
print(10 not in r)
print(9 not in r)



