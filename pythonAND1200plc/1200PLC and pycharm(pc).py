# -*- coding: utf-8 -*-
# 学习Demo
# 创建人：ZY
# 开发时间：2023/3/15 11:11
import snap7
from snap7 import util
import struct
import time

# 创建通讯客户端实例
plcObj = snap7.client.Client()

# 连接至PLC
plcObj.connect('192.168.2.101', 0, 1)

# 测试通讯是否成功
if plcObj.get_connected():
    print('连接成功')
else:
    print('连接失败')

# Bool值的读写：read_area(area, dbnumber, start, size) area：区地址类型（十六进制类型）
# dbnumber：地址编号（int），只适用于DB区和200smart的V区，其它区全默认0，V区只能填1
# start：要读取数据的字节起始地址（int）
# size：要读取的数据类型所占字节长度大小（int）


# Q区 Q0.3
'''byte_arrays1 = plcObj.read_area(snap7.client.Areas.PA, 0, 0, 1)
print('赋值前', util.get_bool(byte_arrays1, 0, 3))
util.set_bool(byte_arrays1, 0, 3, True)
plcObj.write_area(snap7.client.Areas.PA, 0, 0, byte_arrays1)
print('赋值后', util.get_bool(byte_arrays1, 0, 3))
plcObj.disconnect()
plcObj.destroy()'''

# m1000.3 第七轴点检确定
'''byte_arrays1 = plcObj.read_area(snap7.client.Areas.MK, 0, 1000, 1)
print('赋值前', util.get_bool(byte_arrays1, 0, 3))
util.set_bool(byte_arrays1, 0, 3, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 1000, byte_arrays1)
print('赋值后', util.get_bool(byte_arrays1, 0, 3))
plcObj.disconnect()
plcObj.destroy()'''

# m1000.4 气源点检确定
'''byte_arrays1 = plcObj.read_area(snap7.client.Areas.MK, 0, 1000, 1)
print('赋值前', util.get_bool(byte_arrays1, 0, 4))
util.set_bool(byte_arrays1, 0, 4, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 1000, byte_arrays1)
print('赋值后', util.get_bool(byte_arrays1, 0, 4))
plcObj.disconnect()
plcObj.destroy()'''

# m1000.5 焊机点检确定
'''byte_arrays1 = plcObj.read_area(snap7.client.Areas.MK, 0, 1000, 1)
print('赋值前', util.get_bool(byte_arrays1, 0, 5))
util.set_bool(byte_arrays1, 0, 5, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 1000, byte_arrays1)
print('赋值后', util.get_bool(byte_arrays1, 0, 5))
plcObj.disconnect()
plcObj.destroy()'''

# m1000.6 有无异物点检确定
'''byte_arrays1 = plcObj.read_area(snap7.client.Areas.MK, 0, 1000, 1)
print('赋值前', util.get_bool(byte_arrays1, 0, 6))
util.set_bool(byte_arrays1, 0, 6, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 1000, byte_arrays1)
print('赋值后', util.get_bool(byte_arrays1, 0, 6))
plcObj.disconnect()
plcObj.destroy()'''

# m7.0机器人安全位置
'''byte_arrays1 = plcObj.read_area(snap7.client.Areas.MK, 0, 7, 1)
print('赋值前', util.get_bool(byte_arrays1, 0, 0))
util.set_bool(byte_arrays1, 0, 0, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 7, byte_arrays1)
print('赋值后', util.get_bool(byte_arrays1, 0, 0))
plcObj.disconnect()
plcObj.destroy()'''

# 运转准备i1.2-用m1000.7 来连接i1.2 i区状态测试看能否读取i1.2的状态  测试结果可以读取i区状态，但不能强制
# byte_arrays7 = plcObj.read_area(snap7.client.Areas.PE, 0, 1, 1)
# print('赋值前', util.get_bool(byte_arrays7, 0, 2))
'''util.set_bool(byte_arrays7, 0, 2, True)
plcObj.write_area(snap7.client.Areas.PE, 0, 1, byte_arrays7)
print('赋值后', util.get_bool(byte_arrays7, 0, 2))
util.set_bool(byte_arrays7, 0, 2, False)                            # 置位0
plcObj.write_area(snap7.client.Areas.PE, 0, 1, byte_arrays7)
print('当前值', util.get_bool(byte_arrays7, 0, 2))'''
# plcObj.disconnect()
# plcObj.destroy()


# m1000.7 连接i1.2 plc程序暂时没有改
'''byte_arrays1 = plcObj.read_area(snap7.client.Areas.MK, 0, 1000, 1)
print('赋值前', util.get_bool(byte_arrays1, 0, 7))
util.set_bool(byte_arrays1, 0, 7, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 1000, byte_arrays1)
print('赋值后', util.get_bool(byte_arrays1, 0, 7))
time.sleep(3)
util.set_bool(byte_arrays1, 0, 7, False)                            # 置位0
plcObj.write_area(snap7.client.Areas.MK, 0, 1000, byte_arrays1)
print('当前值', util.get_bool(byte_arrays1, 0, 7))
plcObj.disconnect()
plcObj.destroy()'''


# 输送带正转 m1000.0 连接i1.4  注意此处要实体按钮无效plc程序需要改
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 1000, 1)  # 先读出数组
print('赋值前', util.get_bool(byte_arrays, 0, 0))    # 获取
util.set_bool(byte_arrays, 0, 0, True)          # 设置bit位为1
plcObj.write_area(snap7.client.Areas.MK, 0, 1000, byte_arrays)  # 写
print('赋值后', util.get_bool(byte_arrays, 0, 0))
util.set_bool(byte_arrays, 0, 0, False)                            # 置位0
plcObj.write_area(snap7.client.Areas.MK, 0, 1000, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 0))
# 关闭连接
plcObj.disconnect()'''

# 输送带反转 m1001.2 连接i1.5  注意此处要实体按钮无效plc程序需要改
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 1001, 1)  # 先读出数组
print('赋值前', util.get_bool(byte_arrays, 0, 2))    # 获取byte index= 起始值-最小值
util.set_bool(byte_arrays, 0, 2, True)              # 设置bit位为1
plcObj.write_area(snap7.client.Areas.MK, 0, 1001, byte_arrays)     # 写
print('赋值后', util.get_bool(byte_arrays, 0, 2))
util.set_bool(byte_arrays, 0, 2, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 1001, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 2))
plcObj.disconnect()  # 关闭连接
plcObj.destroy()     # 销毁'''


# 正转挡停传感器i3.0状态读取 信号到位为Ture后调用机器人示教程序进行抓取
'''byte_arrays9 = plcObj.read_area(snap7.client.Areas.PE, 0, 3, 1)
print('赋值前', util.get_bool(byte_arrays9, 0, 0))
plcObj.disconnect()
plcObj.destroy()'''

# 上料区挡停伸出信号 手动 m12.2
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 12, 1)  # 先读出数组
print('赋值前', util.get_bool(byte_arrays, 0, 2))    # 获取byte index= 起始值-最小值
util.set_bool(byte_arrays, 0, 2, True)# 设置bit位为1
plcObj.write_area(snap7.client.Areas.MK, 0, 12, byte_arrays)     #
print('赋值后', util.get_bool(byte_arrays, 0, 2))
time.sleep(0.5)
util.set_bool(byte_arrays, 0, 2, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 12, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 2))
plcObj.disconnect()  # 关闭连接
plcObj.destroy()     # 销毁'''

# 上料区挡停缩回信号 手动 m12.3
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 12, 1)  # 先读出数组
print('赋值前', util.get_bool(byte_arrays, 0, 3))    # 获取byte index= 起始值-最小值
util.set_bool(byte_arrays, 0, 3, True)              # 设置bit位为1
plcObj.write_area(snap7.client.Areas.MK, 0, 12, byte_arrays)     # 写
print('赋值后', util.get_bool(byte_arrays, 0, 3))
time.sleep(0.5)
util.set_bool(byte_arrays, 0, 3, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 12, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 3))
plcObj.disconnect()  # 关闭连接
plcObj.destroy()     # 销毁'''

# 第七轴使能键激活-m14.2
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 14, 1)  # 先读出数组
print('赋值前', util.get_bool(byte_arrays, 0, 2))    # 获取byte index= 起始值-最小值
util.set_bool(byte_arrays, 0, 2, True)              # 设置bit位为1
plcObj.write_area(snap7.client.Areas.MK, 0, 14, byte_arrays)     # 写
print('赋值后', util.get_bool(byte_arrays, 0, 2))
time.sleep(0.5)
util.set_bool(byte_arrays, 0, 2, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 14, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 2))
plcObj.disconnect()  # 关闭连接
plcObj.destroy()     # 销毁'''

# 第七轴使能键断开-m14.3
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 14, 1)  # 先读出数组
print('赋值前', util.get_bool(byte_arrays, 0, 3))    # 获取byte index= 起始值-最小值
util.set_bool(byte_arrays, 0, 3, True)              # 设置bit位为1
plcObj.write_area(snap7.client.Areas.MK, 0, 14, byte_arrays)     # 写
print('赋值后', util.get_bool(byte_arrays, 0, 3))
time.sleep(0.5)
util.set_bool(byte_arrays, 0, 3, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 14, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 3))
plcObj.disconnect()  # 关闭连接
plcObj.destroy()     # 销毁'''

# 第七轴异常复位 添加新的M点m1001.3 main程序段4 并联到i1.3上 plc程序未改
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 1001, 1)  # 先读出数组
print('赋值前', util.get_bool(byte_arrays, 0, 3))    # 获取byte index= 起始值-最小值
util.set_bool(byte_arrays, 0, 3, True)              # 设置bit位为1
plcObj.write_area(snap7.client.Areas.MK, 0, 1001, byte_arrays)     # 写
print('赋值后', util.get_bool(byte_arrays, 0, 3))
time.sleep(0.5)
util.set_bool(byte_arrays, 0, 3, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 1001, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 3))
plcObj.disconnect()  # 关闭连接
plcObj.destroy()     # 销毁'''

# 第七轴位置1:MD500 速度1设置:MD504  PLC第一次上电传送该数据 PC初始化时应该传送该数据
# 读MD500双整数类型的当前值
'''dataDint = plcObj.read_area(snap7.client.Areas.MK, 0, 500, 4)
dataDint = util.get_dint(dataDint, 0)
print('赋值前', dataDint)

# 写MD500寄存器
DintData = bytearray(4)             # 数据缓存
util.set_dint(DintData, 0, 1300, )  # 将数据写入缓存
plcObj.write_area(snap7.client.Areas.MK, 0, 500, DintData)
print('赋值后16进制:', DintData)
plcObj.disconnect()  # 关闭连接'''

# 位置1-MD500浮点数读取
'''dataReal1 = plcObj.read_area(snap7.client.Areas.MK, 0, 500, 4)
dataReal1 = util.get_real(dataReal1, 0)
print('赋值前', dataReal1)'''

# 位置1:-MD500浮点数写入-方法一  这里PLC程序增加一个PC写入M1001.4
'''pos1 = bytearray(4)
util.set_real(pos1, 0, 1300)
plcObj.write_area(snap7.client.Areas.MK, 0, 500, pos1)
print('赋值后', pos1)
plcObj.disconnect()'''

# 速度1-MD504浮点数读取
'''dataReal2 = plcObj.read_area(snap7.client.Areas.MK, 0, 504, 4)
dataReal2 = util.get_real(dataReal2, 0)
print('赋值前', dataReal2)'''

# 速度1-MD504浮点数写入-方法一  这里PLC程序增加一个PC写入M1001.4
'''Vel1 = bytearray(4)
util.set_real(Vel1, 0, 50)
plcObj.write_area(snap7.client.Areas.MK, 0, 504, Vel1)
print('赋值后', Vel1)
plcObj.disconnect()'''

# MD500浮点数写入-方法二
'''pos1 = bytearray(4)
pos1 = struct.pack(">f", 100000.0)
plcObj.write_area(snap7.client.Areas.MK, 0, 500, pos1)
print('当前值', struct.pack(">f", 1600.0))
plcObj.disconnect()'''

# 第七轴位置1，速度1  PC写入m1001.4
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 1001, 1)  # 先读出数组
print('赋值前', util.get_bool(byte_arrays, 0, 4))    # 获取byte index= 起始值-最小值
util.set_bool(byte_arrays, 0, 4, True)              # 设置bit位为1
plcObj.write_area(snap7.client.Areas.MK, 0, 1001, byte_arrays)     # 写
print('赋值后', util.get_bool(byte_arrays, 0, 4))
time.sleep(0.5)
util.set_bool(byte_arrays, 0, 4, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 1001, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 4))
plcObj.disconnect()  # 关闭连接
plcObj.destroy()     # 销毁'''

''''# 位置2-MD508浮点数读取
dataReal3 = plcObj.read_area(snap7.client.Areas.MK, 0, 508, 4)
dataReal3 = util.get_real(dataReal3, 0)
print('赋值前', dataReal3)

# 位置2:-MD508浮点数写入
pos2 = bytearray(4)
util.set_real(pos2, 0, 1000)
plcObj.write_area(snap7.client.Areas.MK, 0, 508, pos2)
print('赋值后', pos2)


# 速度2-MD512浮点数读取
dataReal4 = plcObj.read_area(snap7.client.Areas.MK, 0, 512, 4)
dataReal4 = util.get_real(dataReal4, 0)
print('赋值前', dataReal4)

# 速度2-MD512浮点数写入
Vel2 = bytearray(4)
util.set_real(Vel2, 0, 50)
plcObj.write_area(snap7.client.Areas.MK, 0, 512, Vel2)
print('赋值后', Vel2)

# 位置3-MD516浮点数读取
dataReal5 = plcObj.read_area(snap7.client.Areas.MK, 0, 516, 4)
dataReal5 = util.get_real(dataReal5, 0)
print('赋值前', dataReal5)

# 位置3:-MD516浮点数写入
pos3 = bytearray(4)
util.set_real(pos3, 0, -10)
plcObj.write_area(snap7.client.Areas.MK, 0, 516, pos3)
print('赋值后', pos3)


# 速度3-MD520浮点数读取
dataReal6 = plcObj.read_area(snap7.client.Areas.MK, 0, 520, 4)
dataReal6 = util.get_real(dataReal6, 0)
print('赋值前', dataReal6)

# 速度3-MD520浮点数写入
Vel3 = bytearray(4)
util.set_real(Vel3, 0, 100)
plcObj.write_area(snap7.client.Areas.MK, 0, 520, Vel3)
print('赋值后', Vel3)


# 位置4-MD524浮点数读取
dataReal7 = plcObj.read_area(snap7.client.Areas.MK, 0, 524, 4)
dataReal7 = util.get_real(dataReal7, 0)
print('赋值前', dataReal7)

# 位置4:-MD524浮点数写入
pos4 = bytearray(4)
util.set_real(pos4, 0, -999)
plcObj.write_area(snap7.client.Areas.MK, 0, 524, pos4)
print('赋值后', pos4)

# 速度4-MD528浮点数读取
dataReal8 = plcObj.read_area(snap7.client.Areas.MK, 0, 528, 4)
dataReal8 = util.get_real(dataReal8, 0)
print('赋值前', dataReal8)

# 速度4-MD528浮点数写入
Vel4 = bytearray(4)
util.set_real(Vel4, 0, 99)
plcObj.write_area(snap7.client.Areas.MK, 0, 528, Vel4)
print('赋值后', Vel4)

# 第七轴手动运行速度设定 MD550  注意！根据需要确定后期是否需要一键清零功能
Vel5 = bytearray(4)
util.set_real(Vel5, 0, 50)
plcObj.write_area(snap7.client.Areas.MK, 0, 550, Vel5)
print('赋值后', Vel5)
#plcObj.disconnect()'''

# 第七轴正转点动 m100.5
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 100, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 5))
util.set_bool(byte_arrays, 0, 5, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 100, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 5))
time.sleep(0.5)
util.set_bool(byte_arrays, 0, 5, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 100, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 5))
plcObj.disconnect()  # 关闭连接
plcObj.destroy()     # 销毁'''

# 第七轴反转点动 m100.6
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 100, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 6))
util.set_bool(byte_arrays, 0, 6, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 100, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 6))
time.sleep(0.5)
util.set_bool(byte_arrays, 0, 6, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 100, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 6))
plcObj.disconnect()  # 关闭连接
plcObj.destroy()     # 销毁'''

# 第七轴原点回归 m100.3
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 100, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 3))
util.set_bool(byte_arrays, 0, 3, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 100, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 3))
time.sleep(0.5)
util.set_bool(byte_arrays, 0, 3, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 100, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 3))
plcObj.disconnect()  # 关闭连接
plcObj.destroy()     # 销毁'''

# 第七轴原点回到零位指示 m110.5
byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 110, 1)
print('零位指示：', util.get_bool(byte_arrays, 0, 0))

# 取料允许 m6.0一直为Ture 才能自动运行到取料位置
# 手动置位1
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 6, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 0))
util.set_bool(byte_arrays, 0, 0, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 6, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 0))'''
# 手动置位为0
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 6, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 0))
util.set_bool(byte_arrays, 0, 0, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 6, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 0))'''

# 焊接允许 m6.1一直为Ture 才能自动运行到焊接位置
# 手动置位1
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 6, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 1))
util.set_bool(byte_arrays, 0, 1, True)
plcObj.write_area(snap7.client.Areas.MK, 1, 6, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 1))'''
# 手动置位为0
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 6, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 1))
util.set_bool(byte_arrays, 0, 1, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 6, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 1))'''

# 下料允许 m6.2一直为Ture 才能自动运行到下料位置
# 手动置位1
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 6, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 2))
util.set_bool(byte_arrays, 0, 2, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 6, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 2))'''
# 手动置位为0
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 6, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 2))
util.set_bool(byte_arrays, 0, 2, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 6, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 2))'''

# 原点允许 m6.3一直为Ture 自动运行结束时才能自动回到原点
# 手动置位1
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 6, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 3))
util.set_bool(byte_arrays, 0, 3, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 6, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 3))'''
# 手动置位为0
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 6, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 3))
util.set_bool(byte_arrays, 0, 3, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 6, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 3))'''

# 自动取料位 m131.1 沿激活 条件 m6.0
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 131, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 1))
util.set_bool(byte_arrays, 0, 1, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 131, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 1))
time.sleep(0.5)
util.set_bool(byte_arrays, 0, 1, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 131, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 1))
plcObj.disconnect()  # 关闭连接
plcObj.destroy()     # 销毁'''

# 自动焊接位 m131.2 沿激活 条件 m6.1
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 131, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 2))
util.set_bool(byte_arrays, 0, 2, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 131, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 2))
time.sleep(0.5)
util.set_bool(byte_arrays, 0, 2, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 131, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 2))
plcObj.disconnect()  # 关闭连接
plcObj.destroy()     # 销毁'''

# 自动下料位 m131.3 沿激活 条件 m6.2
'''byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 131, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 3))
util.set_bool(byte_arrays, 0, 3, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 131, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 3))
time.sleep(0.5)
util.set_bool(byte_arrays, 0, 3, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 131, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 3))
plcObj.disconnect()  # 关闭连接
plcObj.destroy()     # 销毁'''

# 自动回原点 m131.4 沿激活 条件 m6.3
byte_arrays = plcObj.read_area(snap7.client.Areas.MK, 0, 131, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 4))
util.set_bool(byte_arrays, 0, 4, True)
plcObj.write_area(snap7.client.Areas.MK, 0, 131, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 4))
time.sleep(0.5)
util.set_bool(byte_arrays, 0, 4, False)
plcObj.write_area(snap7.client.Areas.MK, 0, 131, byte_arrays)
print('当前值', util.get_bool(byte_arrays, 0, 4))
plcObj.disconnect()  # 关闭连接
plcObj.destroy()     # 销毁
