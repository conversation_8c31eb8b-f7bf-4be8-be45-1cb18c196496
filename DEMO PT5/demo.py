# import sys
# import requests
# from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QVBoxLayout, QLabel, QComboBox, QMenuBar, QAction, QMessageBox
# from PyQt5.QtWidgets import QApplication, QMainWindow, QToolBar, QAction, QMenuBar, QMenu
# from PyQt5.QtGui import QIcon
# from PyQt5.QtCore import Qt, QTimer, QDate, QTime
#
#
# class MainWindow(QWidget):
#     def __init__(self):
#         super().__init__()
#         self.initUI()
#
#     def main():
#         app = QApplication(sys.argv)
#         #mainWindow = MainWindow()
#         mainWindow.show()
#         sys.exit(app.exec_())
#
#     def initUI(self):
#         self.setWindowTitle('历史上的今天')
#         self.resize(800, 600)
#         self.setStyleSheet("background-color:white;")
#         try:
#             self.setWindowIcon(QIcon('icon.png'))
#         except Exception as e:
#             print(f"Error loading icon: {e}")
#         self.center()
#         self.layout = QVBoxLayout()
#         self.setLayout(self.layout)
#         self.createWidgets()
#         self.createMenuBar()
#         self.createToolBar()
#         self.timer = QTimer(self)
#         self.timer.timeout.connect(self.showTime)
#         self.timer.start(1000)
#         self.show()
#
#     def center(self):
#         qr = self.frameGeometry()
#         cp = QApplication.desktop().screenGeometry().center()
#         qr.moveCenter(cp)
#         self.move(qr.topLeft())
#
#     def createWidgets(self):
#         self.dateLabel = QLabel(self)
#         self.updateDate()
#         self.dateLabel.setAlignment(Qt.AlignCenter)
#         self.layout.addWidget(self.dateLabel)
#
#         self.weekLabel = QLabel(self)
#         self.updateWeek()
#         self.weekLabel.setAlignment(Qt.AlignCenter)
#         self.layout.addWidget(self.weekLabel)
#
#         self.timeLabel = QLabel(self)
#         self.updateTime()
#         self.timeLabel.setAlignment(Qt.AlignCenter)
#         self.layout.addWidget(self.timeLabel)
#
#         self.weatherLabel = QLabel(self)
#         self.weatherLabel.setText('天气')
#         self.weatherLabel.setAlignment(Qt.AlignCenter)
#         self.layout.addWidget(self.weatherLabel)
#
#         self.cityComboBox = QComboBox(self)
#         cities = ['北京', '上海', '广州', '深圳', '杭州', '南京', '苏州']
#         for city in cities:
#             self.cityComboBox.addItem(city)
#         self.cityComboBox.setCurrentIndex(0)
#         self.layout.addWidget(self.cityComboBox)
#
#         self.historyLabel = QLabel(self)
#         self.historyLabel.setText('历史上的今天')
#         self.historyLabel.setAlignment(Qt.AlignCenter)
#         self.layout.addWidget(self.historyLabel)
#
#     def createMenuBar(self):
#         menubar = QMenuBar(self)
#
#         fileMenu = menubar.addMenu('文件')
#         exitAction = QAction(QIcon('icon.png'), '退出', self)
#         exitAction.setShortcut('Ctrl+Q')
#         exitAction.setStatusTip('退出程序')
#         exitAction.triggered.connect(self.close)
#         fileMenu.addAction(exitAction)
#
#         helpMenu = menubar.addMenu('帮助')
#         helpAction = QAction(QIcon('icon.png'), '帮助', self)
#         helpAction.triggered.connect(self.showHelp)
#         helpMenu.addAction(helpAction)
#
#         aboutMenu = menubar.addMenu('关于')
#         aboutAction = QAction(QIcon('icon.png'), '关于', self)
#         aboutAction.triggered.connect(self.showAbout)
#         aboutMenu.addAction(aboutAction)
#
#         self.setMenuBar(menubar)
#
#     def createToolBar(self):
#         self.toolBar = QToolBar(self)
#         self.addToolBar(self.toolBar)
#
#         actions = ['天气', '城市', '历史', '设置']
#         for action in actions:
#             self.toolBar.addAction(QIcon('icon.png'), action)
#
#     def showTime(self):
#         self.updateTime()
#         self.updateDate()
#         self.updateWeek()
#         self.updateWeather()
#
#     def updateTime(self):
#         self.timeLabel.setText(QTime.currentTime().toString('hh:mm:ss'))
#
#     def updateDate(self):
#         self.dateLabel.setText(QDate.currentDate().toString('yyyy年MM月dd日'))
#
#     def updateWeek(self):
#         self.weekLabel.setText(QDate.currentDate().toString('dddd'))
#
#     def updateWeather(self):
#         city = self.cityComboBox.currentText()
#         try:
#             # 这里只是一个示例，你需要替换为实际的API调用
#             response = requests.get(f"http://api.weatherapi.com/v1/current.json?key=your_api_key&q={city}")
#             data = response.json()
#             # 假设API返回信息包含一个名为'current'的字典，其中包含'condition'字典和'temp_c'
#             condition = data['current']['condition']['text']
#             temperature = data['current']['temp_c']
#             self.weatherLabel.setText(f"{city} - {condition}, 当前温度: {temperature}°C")
#         except Exception as e:
#             print(f"Error fetching weather data: {e}")
#             self.weatherLabel.setText('天气信息获取失败')
#
#     def showHistory(self):
#         # 显示历史信息
#         self.historyLabel.setText(f"{QDate.currentDate().toString('yyyy年MM月dd日')}的历史事件")  # 这里只是一个占位符，实际需要从API获取历史信息
#
#     def showSetting(self):
#         # 显示设置
#         QMessageBox.about(self, '设置', '这里可以设置一些选项，比如默认城市、历史事件的时间跨度等。')
#
#     def showAbout(self):
#         QMessageBox.about(self, '关于', '历史上的今天是一个基于PyQt5的GUI程序，实现了历史上的今天的各种信息。')
#
#     def showHelp(self):
#         helpText = ('1. 选择城市，显示该城市的天气预报。\n'
#                     '2. 点击历史按钮，显示当前日期的历史事件。\n'
#                     '3. 点击设置按钮，设置程序的默认城市等。\n'
#                     '4. 点击关于按钮，显示关于信息。\n'
#                     '5. 点击帮助按钮，显示帮助信息。\n'
#                     '6. 点击退出按钮，退出程序。')
#         QMessageBox.about(self, '帮助', helpText)
#
#
# if __name__ == '__main__':
#     #MainWindow.main()
#     app = QApplication(sys.argv)
#     #mainWindow = MainWindow()
#     sys.exit(app.exec_())

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QComboBox, QMenuBar, QAction, QMessageBox

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        self.setWindowTitle('历史上的今天')
        self.resize(800, 600)
        self.setStyleSheet("background-color:white;")

def main():
    app = QApplication(sys.argv)
    mainWindow = MainWindow()
    mainWindow.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
