#请使用PyQt5编写一个GUI程序，实现如下功能：
#1. 显示当前时间
#2. 显示当前日期
#3. 显示当前星期
#4. 显示今天的天气
#5. 显示当前温度状况（可选：显示明天的天气状况）
#6. 显示当前城市的天气预报（可选：显示其他城市的天气预报）
#7. 显示历史上的今天（可选：显示历史上的其他日期）
#8. 显示历史上的今天的事件（可选：显示历史上的其他日期的事件）
#9. 显示历史上的今天的图片（可选：显示历史上的其他日期的图片）
#10. 显示历史上的今天的视频（可选：显示历史上的其他日期的视频）
#11. 显示历史上的今天的音乐（可选：显示历史上的其他日期的音乐）
#12. 显示历史上的今天的新闻（可选：显示历史上的其他日期的新闻）
#13. 显示历史上的今天的股票信息（可选：显示历史上的其他日期的股票信息）
#14. 显示历史上的今天的财经信息（可选：显示历史上的其他日期的财经信息）
#15. 显示历史上的今天的彩票信息（可选：显示历史上的其他日期的彩票信息）
#16. 显示历史上的今天的体育比赛（可选：显示历史上的其他日期的体育比赛）
#17. 显示历史上的今天的运动比赛（可选：显示历史上的其他日期的运动比赛）
#18. 显示历史上的今天的旅游景点（可选：显示历史上的其他日期的旅游景点）
#19. 显示历史上的今天的美食（可选：显示历史上的其他日期的美食）
#20. 显示历史上的今天的宗教活动（可选：显示历史上的其他日期的宗教活动）
#21. 显示历史上的今天的节日（可选：显示历史上的其他日期的节日）
#22. 显示历史上的今天的其他信息（可选：显示历史上的其他日期的其他信息）
#23. 显示历史上的今天的其他信息（可选：显示历史上的其他日期的其他信息）开始编写代码
#导入必要的模块
import sys

#导入PyQt5模块
from PyQt5.QtGui import QIcon, QFont, QColor, QPixmap, QImage, QPalette, QBrush, QPen, QPainter, QCursor, QKeySequence
from PyQt5.QtCore import *
from PyQt5.QtMultimedia import *
from PyQt5.QtMultimediaWidgets import *
from PyQt5.QtNetwork import *
from PyQt5.QtNetworkAuth import *
from PyQt5.QtWebEngineWidgets import *
from PyQt5.QtWebChannel import *
from PyQt5.QtPrintSupport import *
from PyQt5.QtSerialPort import *
from PyQt5.QtSql import *
from PyQt5.QtSvg import *
from PyQt5.QtTest import *
from PyQt5.QtWebKit import *
from PyQt5.QtWebKitWidgets import * #导入必要的模块
from PyQt5.QtWebSockets import *
from PyQt5.QtWidgets import *
from PyQt5.QtWinExtras import *
from PyQt5.QtX11Extras import *
from PyQt5.QtXml import *
from PyQt5.QtXmlPatterns import *
from PyQt5.QtQuick import *
from PyQt5.QtQuickWidgets import *
from PyQt5.QtQml import *    #导入必要的模块

#定义全局变量
#定义主窗口类
class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()

        def initUI(self):
            #设置窗口标题
            self.setWindowTitle('历史上的今天')
            #设置窗口大小
            self.resize(800, 600)
            #设置窗口背景色
            self.setStyleSheet("background-color:white;")
            #设置窗口图标
            self.setWindowIcon(QIcon('icon.png'))
            #设置窗口居中
            self.center()
            #设置窗口布局
            self.layout = QVBoxLayout()
            self.setLayout(self.layout)
            #创建控件
            self.createWidgets()
            #创建菜单栏
            self.createMenuBar()
            #创建状态栏
            self.createStatusBar()
            #创建工具栏
            self.createToolBar()
            #创建定时器
            self.timer = QTimer(self)
            self.timer.timeout.connect(self.showTime)
            self.timer.start(1000)
            #显示窗口
            self.show()

    def center(self):
        qr = self.frameGeometry()
        cp = QDesktopWidget().availableGeometry().center()
        qr.moveCenter(cp)
        self.move(qr.topLeft())

    def createWidgets(self):
        #创建日期控件
        self.dateLabel = QLabel(self)
        self.dateLabel.setText(QDate.currentDate().toString('yyyy年MM月dd日'))
        self.dateLabel.setAlignment(Qt.AlignCenter)
        self.layout.addWidget(self.dateLabel)
        #创建星期控件
        self.weekLabel = QLabel(self)
        self.weekLabel.setText(QDate.currentDate().toString('dddd'))
        self.weekLabel.setAlignment(Qt.AlignCenter)
        self.layout.addWidget(self.weekLabel)
        #创建时间控件
        self.timeLabel = QLabel(self)
        self.timeLabel.setText(QTime.currentTime().toString('hh:mm:ss'))
        self.timeLabel.setAlignment(Qt.AlignCenter)
        self.layout.addWidget(self.timeLabel)
        #创建天气控件
        self.weatherLabel = QLabel(self)
        self.weatherLabel.setText('天气')
        self.weatherLabel.setAlignment(Qt.AlignCenter)
        self.layout.addWidget(self.weatherLabel)
            #创建城市选择控件
        self.cityComboBox = QComboBox(self)
        self.cityComboBox.addItem('北京')
        self.cityComboBox.addItem('上海')
        self.cityComboBox.addItem('广州')
        self.cityComboBox.addItem('深圳')
        self.cityComboBox.addItem('杭州')
        self.cityComboBox.addItem('南京')
        self.cityComboBox.addItem('苏州')


    def createMenuBar(self):
        #创建菜单栏

        def createStatusBar(self):
            #创建状态栏
            self.statusBar = QStatusBar(self)
            self.setStatusBar(self.statusBar)
            self.statusBar.showMessage('欢迎使用历史上的今天')

    def createToolBar(self):
        #创建工具栏
        self.toolBar = QToolBar(self)
        self.addToolBar(self.toolBar)
        #创建工具栏按钮
        self.toolBar.addAction(QIcon('icon.png'), '天气')
        self.toolBar.addAction(QIcon('icon.png'), '城市')
        self.toolBar.addAction(QIcon('icon.png'), '历史')
        self.toolBar.addAction(QIcon('icon.png'), '设置')

    def showTime(self):
        #显示时间
        self.timeLabel.setText(QTime.currentTime().toString('hh:mm:ss'))
        #显示日期
        self.dateLabel.setText(QDate.currentDate().toString('yyyy年MM月dd日'))
        #显示星期
        self.weekLabel.setText(QDate.currentDate().toString('dddd'))
        #显示天气
        self.weatherLabel.setText('天气')
        #显示城市
        self.cityComboBox.setCurrentIndex(0)
        #显示历史

    def showWeather(self):
        #显示天气
        self.weatherLabel.setText('天气')

    def showCity(self):
        #显示城市
        self.cityComboBox.setCurrentIndex(0)

    def showHistory(self):
        #显示历史
        self.historyLabel.setText('历史')

    def showSetting(self):
        #显示设置
        self.settingLabel.setText('设置')

    def showAbout(self):
        #显示关于
        QMessageBox.about(self, '关于', '历史上的今天是一个基于PyQt5的GUI程序，实现了历史上的今天的各种信息。')

    def showHelp(self):
        #显示帮助
        QMessageBox.about(self, '帮助', '1. 点击天气按钮，显示当前城市的天气预报。\n2. 点击城市按钮，显示当前城市的天气预报。\n3. 点击历史按钮，显示历史上的今天。\n4. 点击设置按钮，显示程序设置。\n5. 点击关于按钮，显示关于信息。\n6. 点击帮助按钮，显示帮助信息。\n7. 点击退出按钮，退出程序。')

    def closeEvent(self, event):
        #关闭事件
        reply = QMessageBox.question(self, '退出', '确认退出吗？', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    mainWindow = MainWindow()
    sys.exit(app.exec_())



















































































