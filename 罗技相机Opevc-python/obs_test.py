import time
import cv2


cameras = {
    "cam_top_left": cv2.VideoCapture(700),
    #"cam_top_right": cv2.VideoCapture(701),
    #"wrist_right": cv2.VideoCapture(703)
}


def main():
    # 创建一个VideoCapture对象，参数是摄像头的索引或视频文件路径
    # 检查是否成功打开摄像头
    for cam_name in cameras:
        if not cameras[cam_name].isOpened():
            print("无法打开摄像头: ".format(cam_name))
            exit()

    # 设置是否自动对焦，1为True，2为False
    print("开始设置各相机参数...")
    for cam_name in cameras:
        cap = cameras[cam_name]
        cap.set(cv2.CAP_PROP_AUTOFOCUS, 1)

        cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter.fourcc('M', 'J', 'P', 'G'))
        cap.set(cv2.CAP_PROP_FPS, 60)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        # cap.set(cv2.CAP_PROP_FPS, 60)
    start_time = time.time()

    print("开始进行图像采集...")
    while True:  # 从摄像头捕获一帧图像
        for cam_name in cameras:
            ret, frame = cameras[cam_name].read()  # 如果读取帧失败，则退出循环
            if not ret:
                print("无法获取帧，检查摄像头是否还在工作")
                break
            # 显示捕获到的帧
            cv2.imshow(cam_name, frame)
            cv2.waitKey(1)
            now_time = time.time()
            duration = now_time - start_time
            # fps = 1 / duration
            start_time = now_time
            # print(f"fps, {fps}")
    # 释放摄像头资源
    for cam_name in cameras:
        cap = cameras[cam_name]
        cap.release()
        # 关闭窗口
        cv2.destroyAllWindows()


if __name__ == "__main__":
    main()
