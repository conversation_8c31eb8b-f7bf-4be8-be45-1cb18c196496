import snap7                     # 安装注意事项，除了在cmd安装，还要在pycharm中安装，file-setting-python interperter 添加按钮
from snap7.util import *         # 对位操作的函数要导入该库
from snap7 import util
import time
import struct
client = snap7.client.Client()   # 创建通讯客户端实例
client.connect('*************', 0, 1)   # 连接至plc ip地址，机架，槽位
# 测试通讯是否成功
if client.get_connected():
    print('连接成功')
else:
    print('连接失败')

# 写入DB10.0 —— bool值
#plcObj.write_area(snap7.client.Areas.DB, 10, 0, bool.to_bytes(False, 1, 'big'))

# 写入DB10.2
#plcObj.write_area(snap7.client.Areas.DB, 10, 2, int.to_bytes(200, 2, 'big'))
# plcObj.write_area(snap7.client.Areas.DB, 10, 2, struct.pack(">h", 112))

# 写入DB10.4 —— real值
client.write_area(snap7.client.Areas.DB, 100, 4, struct.pack(">f", 10.6))

# 写入DB10.8 —— string值
# str = 'hello python'
# data = int.to_bytes(254, 1, 'big') + int.to_bytes(len(str), 1, 'big') + str.encode(encoding='ascii')
# plcObj.write_area(snap7.client.Areas.DB, 10, 8, data)

# 写入DB10.264 —— wstring值
# str = '中国北京市'
# data = int.to_bytes(508, 2, 'big') + int.to_bytes(len(str), 2, 'big') + str.encode(encoding='utf-16be')
# plcObj.write_area(snap7.client.Areas.DB, 10, 264, data)

# 关闭连接
client.disconnect()