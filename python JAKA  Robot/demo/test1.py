# # -*- coding: utf-8 -*-
# 学习Demo
# 创建人：ZY
# 开发时间：2022/12/2 14:40
import ctypes
from ctypes import *
import sys
import jkrc
import time
#import math

PI = 3.1415926

robot = jkrc.RC("*************")

#address = 'C:\\Users\\<USER>\\weldingline\\Lib\\site-packages\\jakaAPI.dll'

#dell = cdll.LoadLibrary(address)    # 动态库连接
#print(dell)

#sys.path.append('C:\\Users\\<USER>\\weldingline\\Lib\\site-packages\\jkrc.pyd')
# pyd 是指将Python源码翻译成.c文件（不可逆),gcc+vs,将.c文件翻译成pyd.加密过的模块；pyc-字节码（加速程序运行，加密型差）

IO_CABINET = 0    # 控制柜面板i/o

ret = robot.login()         # 登录


ret = robot.get_joint_position()  # 获取关节位置,这里输出的是弧度，根据需求转换，转换实例见53页
if ret[0] == 0:
    print('关节位置是：', ret[1])
else:
    print('发生一些未知事件，代码是：', ret[0])
#robot.power_on()      # 上电
#robot.enable_robot()  # 使能


print('---------------------数字量输出--------------------------------')
ret1 = robot.get_digital_output(0, 2)        # 获取数字量输出状态
if ret1[0] == 0:
    print('1-数字输出DO2状态是：', ret1[1])
else:
    print('发生一些未知事件，代码是：', ret1[0])

robot.set_digital_output(IO_CABINET, 2, 0)   # 设置控制柜数字输出信号（0对应DO1,1对应DO2，2对应DO3......）第一个1表示控制柜DO索引，
time.sleep(0.1)                              # 第二个‘1’表示：对应DO的BOOL状态
ret1 = robot.get_digital_output(0, 2)
ret1 = robot.get_digital_output(0, 2)
if ret1[0] == 0:
    print('2-数字输出DO2状态是：', ret1[1])
else:

    print('发生一些未知事件，代码是：', ret1[0])

print('------------模拟量输出----------------')
robot.set_analog_output(iotype=IO_CABINET, index=36, value=1.55)  # 设置模拟量输出AO37=index=36 偏移一位 MODBUS地址+1
ret2 = robot.get_analog_output(iotype=IO_CABINET, index=35)  # 获取AO36模拟量的数据
print('AO36模拟量数据是：', ret2[1])

print('------------直线运动-------------------')

# 运动模式
#ABS = 0     # 绝对
#INCR = 1    # 相对
#tcp_pos = [0, 0, 30, 0, 0, 0]
#print("move1")
# 阻塞 沿Z轴负方向 以10mm/s的速度 运动30mm
#ret3 = robot.linear_move(tcp_pos, INCR, True, 10)
# linear_move=（机器人末端运动目标位置,INCR=运动模式，是否为阻塞接口，直线运动速度，单位：mm/s）
#print(ret3[0])
#time.sleep(3)

print('-----------使用APP脚本程序------------')
# 加载通过APP编写的脚本， 这里我自己在APP上编写了一个简单的运动程序名：Load_Program_test
ret4 = robot.program_load("jaka471")
ret4 = robot.get_loaded_program()
print("加载的程序名是：", ret4[1])
time.sleep(5)

# 运行当前加载作业程序,这里还需要做一些判断，参考二次开发37-38页
robot.program_run()     # 运行
time.sleep(5)
#robot.program_pause()   # 暂停
#time.sleep(5)
#robot.program_abort()   # 停止
#time.sleep(60)

#robot.disable_robot()    # 机器人下使能
#time.sleep(3)

#robot.shut_down()        # 机器人控制柜关机
#time.sleep(3)

#robot.power_off()        # 机器人断电
#time.sleep(8)

robot.logout()           # 断开控制器连接






















