#帮我用Python写一段读取发那科机床状态的程序，可以把读取到的信息显示在状态栏上。
#首先，我们需要导入必要的模块：
import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QStatusBar, QPushButton, QVBoxLayout
from PyQt6.QtCore import QThread, pyqtSignal
import serial

#然后，我们定义一个类来读取发那科机床的状态：
class ReadStatusThread(QThread):
    statusSignal = pyqtSignal(str)
    def __init__(self, port):
        super().__init__()
        self.port = port

    def run(self):
        with serial.Serial(self.port, 9600, timeout=1) as ser:
            while True:
                try:
                    data = ser.readline().decode().strip()
                    if data:
                        self.statusSignal.emit(data)
                except serial.SerialException:
                    break

#这个类继承了QThread类，并定义了一个信号statusSignal，这个信号用于将读取到的信息发送给状态栏。
#在run()方法中，我们使用with语句来打开串口，并使用while循环来读取数据。
#如果读取到数据，我们将数据发送给statusSignal信号。
#如果出现异常，我们会退出循环。

#接下来，我们定义一个窗口类来显示状态栏：
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("发那科机床状态")
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.button = QPushButton("读取状态")
        self.button.clicked.connect(self.readStatus)
        layout = QVBoxLayout()
        layout.addWidget(self.button)
        #self.centralWidget().setLayout(layout)

    def readStatus(self):
        self.statusBar.showMessage("正在读取...")
        self.thread = ReadStatusThread("COM3")   # 这里填写串口号
        self.thread.statusSignal.connect(self.showStatus)
        self.thread.start()

    def showStatus(self, status):
        self.statusBar.showMessage(status)

#这个类继承了QMainWindow类，并定义了一个状态栏和一个按钮。

#最后，我们创建一个应用对象，并实例化窗口对象：
if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())     # 运行应用并退出
