# -*- coding: utf-8 -*-
# 学习Demo
# 创建人：ZY
# 开发时间：2023/3/14 13:15
'''import snap7                     # 安装注意事项，除了在cmd安装，还要在pycharm中安装，file-setting-python interperter 添加按钮
from snap7.util import *         # 对位操作的函数要导入该库
from snap7 import util
import time
import struct
client = snap7.client.Client()   # 创建通讯客户端实例
client.connect('*************', 0, 1)   # 连接至plc ip地址，机架，槽位
# 测试通讯是否成功
if client.get_connected():
    print('连接成功')
else:
    print('连接失败')
# 主要通过read_area(area ,dbnumber,start,size) 和write_area 两个函数实现对plc数据的读写
# area 参数用于区分I Q M DB存储区  dbnumber:0 start:起始地址 size：读取长度'''
'''area：区地址类型（十六进制类型），如下图对应 dbnumber：地址编号（int），只适用于DB区和200samart的V区，其它区全默认0，
V区只能填1 start：要读取数据的字节起始地址（int） size：要读取的数据类型所占字节长度大小（int），如下字典对应'''
'''areas = ADict({0x83
    'PE':0x81,   # input 输入区
    'PA':0x82,   # output 输出区
    'MK':0x83,   # bit memory中间存储区
    'DB':0x84,   # DB区
    'CT':0x1C,   # counters
    'TM':0x1D,   # Timers
})'''
'''先计算需要读取的总字节总数例：读取DB10的五个变量-在焊接电源test中建立'''
'''data = client.db_read(10, 0, 30)   # db_read 读取DB块的方法 10是DB号 0是起始地址 776是总字节数 date变量进行接收'''
'''另一种写法'''
#data1 = client.read_area(snap7.client.Areas.DB, 10, 0, 776)
'''read_area是读取任意区域的方法，通过第一个参数的枚举来区分读取的区域，
如input、output、DB等，后面三个参数与db_read一致。
当我们需要的数据以字节的形式读取上来以后，我们就可以进行解析了'''


# 数据解析：第一种是使用python自身的数据类型转换的方法进行解析，第二种是使用python-snap7提供的转换方法进行解析
'''第一种:在python中，bool、int两个类型都有一个from_bytes的方法，可以通过这个方法来将字节数组转换为对应的数据。
但是需要注意的是，在PLC中，数据是大端存储的，而PC中一般是小端存储，所以在这样进行转换的时候，需要加上byteorder='big'
，来声明读取上来的字节是大端存储的方式。故前面的bool和int变量都可以以这种方式进行解析'''

# 读取bool的值

#bool.from_bytes(data[0:1], byteorder='big')

# 读取int的值

#int.from_bytes(data[2:4], byteorder='big')
'''[ 数字 : 数字 ]是python截取数组的语法，左边的数字是截取的起始索引号，右边的是截取的停止的索引号，需要注意的是，
右边的索引是不会被截取的，所以像上面代码的data[ 0 : 1 ]，实际上截取的只有data[0]，而data[ 2 : 4 ]，
截取的则是data[2]和data[3]。如果左边的数字不填，则默认从索引0开始截取；而如果右边的数字不填，则默认截取剩余的所有元素。
而对于字符串，python提供了decode方法，可以将字符串的字节数组按照指定的编码格式来转换为字符串。不过PLC中的字符串的头字节
是字符串的变量长度和字符串的实际长度，所以需要跳过这些字节来读取实际的数据,string类型是单字节存储的ASCII编码的字符串，
所以跳过前两个字节。而wstring类型则是双字节存储的UTF-16BE编码的字符串，所以需要跳过前四个字节。'''

# 读取string的值
#data[10:264].decode(encoding="ascii")
# 读取wstring的值
#data[268:].decode(encoding="utf-16be")

'''在python中，float类型没有像bool和int一样的from_bytes方法，这个时候需要使用struct来进行解析
struct是python的模块之一，可以用以解析字节，bool、int和字符串，float也是可以的
struct有pack和unpack两个方法，分别用以将数据转换为字节流和将字节流解析为对应的数据，如下所示，即可将real数值转换为python的float类型'''

# 读取real的值
#struct.unpack('>f', data[4:8])[0]

'''第一个参数是指定需要转换成的数据类型，“f”代表float类型，前面加的“>”代表这个字节数组是大端存储的方式，
第二个参数是需要转换的字节流。由于unpack是以元组的形式返回的数据，所以需要加[0]来获取它返回的第一个数据'''

# python解析(汇总）



'''selfInt = int.from_bytes(data[2:4], byteorder='big')
selfReal = struct.unpack('>f', data[4:8])[0]
selfString = data[10:264].decode(encoding="ascii")
selfWString = data[268:].decode(encoding="utf-16be")
print("python自身函数解析：")
print(
    f"bool:{selfBool}; int:{selfInt}; real:{selfReal}; string:{selfString}; wstring:{selfWString}"
)
#python-snap7函数提供的方法解析：

#我们在使用snap7的转换方法的时候，只需要把我们读取到的字节数组以及数据的起始索引传进去即可，比起使用python自身的方法会更加简单。
#get_bool方法的第三个参数为该字节的第几个bool量，因为一个bool量只需要一个位来表示，而一个字节是包含八个位的，也就是说这个字节
# 可以表示八个bool量，在这里对应的DB10里地址为0.0~0.7的八个bool量，由于我们要读取的是地址0上的第一个bool量，所以第二个参数和
# 第三个参数分别为0，0。\n\nget_string方法的第三个参数为该字符串的最大长度，由于string类型共有256个字节，所以此处填256。'''
'''bool：get_bool(_bytearray, byte_index, bool_index)\n\n _bytearray：字节数组，就是你上面读到的字节数组\n\n
 byte_index：字节索引，这里填0就可以，后面我会详细介绍byte_index和上面read_area()的参数start，size三者的关系，以及灵活应用
 \n\n bool_index： bool值索引，其实就是位(bit)索引（0~7），因为1byte=8bit'''

'''首先导入util, 可以少写个snap7,提高代码的简明性：from snap7 import util'''

'''# Bool的值
data = util.get_bool(data, 0, 0)
# Int的值
data = util.get_int(data, 2)
# Real的值
util.get_real(data, 4)
# String的值
util.get_string(data, 8, 256)'''

# snap7解析
'''snap7Bool = util.get_bool(data, 0, 0)
snap7Int = util.get_int(data, 2)
snap7Real = util.get_real(data, 4)
snap7String = util.get_string(data, 8, 256)
snap7WString = util.get_string(data, 264, 508)
print("snap7函数解析：")
print(
    f"bool:{snap7Bool}; int:{selfInt}; real:{snap7Real}; string:{snap7String}; wstring:{snap7WString}"
)

#  可以看到，wstring读取出来的结果是乱码，这是因为python-snap7的头字节解析及编码格式问题，所以当我们读取wstring的时候，
#  最好还是用python自己的decode方法

#PLC数据写入---与读取数据一样，写入数据也有两种方式，但是区别也仅仅是生成写入数据的字节方式不用。写入数据可以调用
#db_write方法，也可以调用write_area，与读取数据一样，前者只能用以写入DB块，后者可以写入任意区域
#client.write_area(snap7.client.Areas.DB, 10, 0, data)
#client.db_write(10, 0, data)
# data是需要写入的数据的字节形式

#我们在解析读取数据的时候提到，bool和int有from_bytes，同样的它们也有to_bytes方法，用以将数据转换成对应的字节数组的形式：
# bool的字节
#bool.to_bytes(True, 1, 'big')

# int的字节数组（双字节）
#int.to_bytes(200, 2, 'big')

# 字符串类型有解码的decode方法，也有转码的encode方法：

# string的字节数组
#str = 'hello python'
str.encode(encoding='ascii')

# wstring的字节数组
str = '中国北京市'
str.encode(encoding='utf-16be')

# 而对于float类型要用的struct，我们解析数据时用到了它的unpack方法，而转换为字节的时候，我们就需要调用它的pack方法：
# float的字节数组
struct.pack(">f", 10.1)

# 所以我们能很轻易地写出写入数据的代码
import snap7
import struct

# 创建通讯客户端实例
plcObj = snap7.client.Client()

# 连接至PLC
plcObj.connect('192.168.10.230', 0, 1)

# 写入DB10.0 —— bool值
plcObj.write_area(snap7.client.Areas.DB, 10, 0, bool.to_bytes(False, 1, 'big'))

# 写入DB10.2
plcObj.write_area(snap7.client.Areas.DB, 10, 2, int.to_bytes(200, 2, 'big'))
# plcObj.write_area(snap7.client.Areas.DB, 10, 2, struct.pack(">h", 112))

# 写入DB10.4 —— real值
plcObj.write_area(snap7.client.Areas.DB, 10, 4, struct.pack(">f", 10.1))

# 写入DB10.8 —— string值
str = 'hello python'
data = int.to_bytes(254, 1, 'big') + int.to_bytes(len(str), 1, 'big') + str.encode(encoding='ascii')
plcObj.write_area(snap7.client.Areas.DB, 10, 8, data)

# 写入DB10.264 —— wstring值
str = '中国北京市'
data = int.to_bytes(508, 2, 'big') + int.to_bytes(len(str), 2, 'big') + str.encode(encoding='utf-16be')
plcObj.write_area(snap7.client.Areas.DB, 10, 264, data)

# 关闭连接
plcObj.disconnect()


# 根据snap7 util的五种变量类型，可以写出对应的转换代码：
# bool的字节数组
boolData = bytearray(1)
util.set_bool(boolData, 0, 0, True)

# int的字节数组
intData = bytearray(2)
util.set_int(intData, 0, 100)

# real的字节数组
realData = bytearray(4)
util.set_real(realData, 0, 20.5)

# string的字节数组
str = "hello snap7"
stringData = bytearray(len(str) + 2)
util.set_string(stringData, 0, str, 256)
stringData[0] = 254

#需要注意，string类型在调用了set_string方法后，首字节的字符最大值依旧是0，
#可能是python-snap7目前仍然存在的小bug，所以需要手动修改为真实的最大值。
#由于目前并没有set_wstring方法，而且set_string方法也不支持wstring，
#所以wstring依然使用python的decode方法进行写入。写入代码如下：
# import snap7
# from snap7 import util

# 创建通讯客户端实例
plcObj = snap7.client.Client()

# 连接至PLC
plcObj.connect('192.168.10.230', 0, 1)

# 写入bool
boolData = bytearray(1)
util.set_bool(boolData, 0, 0, True)
plcObj.db_write(10, 0, boolData)

# 写入int
intData = bytearray(2)
util.set_int(intData, 0, 100)
plcObj.db_write(10, 2, intData)

# 写入real
realData = bytearray(4)
util.set_real(realData, 0, 20.5)
plcObj.db_write(10, 4, realData)

# 写入string
str = "hello snap7"
stringData = bytearray(len(str) + 2)
util.set_string(stringData, 0, str, 256)
stringData[0] = 254
plcObj.db_write(10, 8, stringData)

# 写入wstring
str = '中国广州市'
data = int.to_bytes(508, 2, 'big') + int.to_bytes(len(str), 2, 'big') + str.encode(encoding='utf-16be')
plcObj.db_write(10, 264, data)

plcObj.disconnect()




"""
     示例
 plc:    s7-1200
 变量地址：[DB4.DBX0.1， DB4.DBD36， DB4.DBW2 .....]
 类型：   [bool, float, word ......]
"""
# from snap7 import util, client
# from snap7.snap7types import S7AreaDB

my_plc = client.Client()
my_plc.connect('192.168.2.1', 0, 0)

byte_arrays = my_plc.read_area(S7AreaDB, 4, 0, 40)
# 这是所有db块，地址编号4的变量，套用图上公公式，最小的起始值是0，size是最大起始值加它类型所占的字节数就是36+float类型所占4个byte长度，所以size是40

value1 = util.get_bool(byte_arrays, 0, 1)
# DB4.DBX0.1是bool类型，byte_index = 起始值是0 - 最小的起始值0 = 0

value2 = util.get_real(byte_arrays, 36)
# DB4.DBD36是float类型，byte_index = 起始值是36 - 最小的起始值0 = 36

value3 = util.get_word(byte_arrays, 2)
# DB4.DBW2是word类型，byte_index = 起始值是2 - 最小的起始值0 = 2

my_plc.disconnect()
my_plc.destroy()
print(value1, value2, value3)


"""
     简单示例#1
 plc:    s7-200SMART
 变量地址：M1.0 (1是起始值，0是bool索引)
 类型：   bool
"""
#from snap7 import util, client
#from snap7.snap7types import S7AreaMK

#my_plc = client.Client()
#my_plc.set_connection_type(3)
#my_plc.connect('*************', 0, 1)
#byte_arrays = my_plc.read_area(S7AreaMK, 0, 1, 1)
#print('赋值前', util.get_bool(byte_arrays, 0, 0))
#util.set_bool(byte_arrays, 0, 0, 1)
#my_plc.write_area(S7AreaMK, 0, 1, byte_arrays)
#print('赋值后', util.get_bool(byte_arrays, 0, 0))
#my_plc.disconnect()
#my_plc.destroy()

-----------------------------------------------------------------------------------------

"""
     简单示例#2
 plc:    s7-1200
 变量地址：Q1.2 (1是起始值，2是bool索引)
 类型：   bool
"""

from snap7 import util, client
from snap7.snap7types import S7AreaPA

my_plc = client.Client()
my_plc.connect('192.168.2.1', 0, 1)
byte_arrays = my_plc.read_area(S7AreaPA, 0, 1, 1)
print('赋值前', util.get_bool(byte_arrays, 0, 2))
util.set_bool(byte_arrays, 0, 2, 1)
my_plc.write_area(S7AreaPA, 0, 1, byte_arrays)
print('赋值后', util.get_bool(byte_arrays, 0, 2))
my_plc.disconnect()
my_plc.destroy()'''